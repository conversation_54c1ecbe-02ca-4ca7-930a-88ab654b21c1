{"name": "website-aizawl-garbo", "type": "module", "private": true, "scripts": {"build:production": "react-router build", "build:staging": "NODE_ENV=test react-router build", "dev": "react-router dev", "start:production": "PORT=3000 react-router-serve ./build/server/index.js", "start:staging": "NODE_ENV=test PORT=3001 react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "codegen": "graphql-codegen --config codegen.ts", "prepare": "husky"}, "dependencies": {"@graphql-typed-document-node/core": "^3.2.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@react-google-maps/api": "^2.20.7", "@react-router/fs-routes": "^7.6.3", "@react-router/node": "^7.6.3", "@react-router/serve": "^7.6.3", "@tanstack/react-form": "^1.14.1", "@tanstack/react-query": "^5.81.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "graphql": "^16.11.0", "graphql-request": "^7.2.0", "isbot": "^5.1.28", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.6.3", "recharts": "2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "use-debounce": "^10.0.5", "zod": "^4.0.10"}, "devDependencies": {"@antfu/eslint-config": "^4.16.2", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint-react/eslint-plugin": "^1.52.2", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/client-preset": "^4.8.3", "@graphql-codegen/introspection": "^4.0.3", "@react-router/dev": "^7.6.3", "@tailwindcss/vite": "^4.1.11", "@types/node": "^24", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.30.1", "eslint-plugin-better-tailwindcss": "^3.4.4", "eslint-plugin-format": "^1.0.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "husky": "^9.1.7", "lint-staged": "^16.1.2", "react-router-devtools": "^5.0.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite": "^7.0.3", "vite-tsconfig-paths": "^5.1.4"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "volta": {"node": "22.11.0"}}