import type { NavigateFunction } from 'react-router'
import { CHECK_PAYMENT_STATUS } from '~/graphql/queries/check-payment-status'
import { graphqlClient } from './graphql-client'
import { rzpayKey } from './razorpay-key'

declare global {
  interface Window {
    Razorpay: any
  }
}

interface RazorpayResponse {
  razorpay_order_id: string
  razorpay_payment_id: string
}

interface Props {
  navigate: NavigateFunction
  orderId: string
  // phoneNumber: string
  // totalAmount: number
}

async function pollPaymentStatus(orderId: string, paymentId: string, maxRetries = 10, interval = 2000) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const client = await graphqlClient()
      const result = await client.request({
        document: CHECK_PAYMENT_STATUS,
        variables: {
          order_id: orderId,
          payment_id: paymentId,
        },
      })

      if (result.checkPaymentStatus?.id) {
        return { success: true, data: result.checkPaymentStatus }
      }

      // If not the last attempt, wait before retrying
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, interval))
      }
    }
    catch (error) {
      console.error(`Payment status check attempt ${attempt} failed:`, error)

      // If not the last attempt, wait before retrying
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, interval))
      }
    }
  }

  return { success: false }
}

export async function handleRazorpay({
  navigate,
  orderId,
  // phoneNumber,
  // totalAmount,
}: Props) {
  try {
    const options = {
      // amount: 10000,
      config: {
        display: {
          blocks: {
            card: {
              instruments: [
                {
                  method: 'card',
                },
              ],
              name: 'Pay with card',
            },
            upi: {
              instruments: [
                {
                  apps: [
                    'google_pay',
                    'paytm',
                    'phonepe',
                    'bhim',
                    'amazon',
                    'airtel',
                    'sbi',
                  ],
                  flows: ['qr', 'collect', 'intent'],
                  method: 'upi',
                },
              ],
              name: 'Pay with UPI',
            },
            wallet: {
              instruments: [
                {
                  method: 'wallet',
                },
              ],
              name: 'Pay with wallet',
            },
          },
          preferences: {
            show_default_blocks: true,
          },
          sequence: ['block.upi', 'card', 'wallet'],
        },
      },
      currency: 'INR',
      handler: async (response: RazorpayResponse) => {
        try {
          // Show loading state
          // setGlobalLoading(true)

          // Poll payment status with retries
          const result = await pollPaymentStatus(
            response.razorpay_order_id,
            response.razorpay_payment_id,
            10, // max retries
            2000, // 2 second interval
          )

          if (result.success) {
            // Pass payment data via router state
            navigate('/payment-success', {
              state: {
                order_id: response.razorpay_order_id,
                payment_id: response.razorpay_payment_id,
                payment_receipt_link: result?.data?.payment_receipt_link,
                payable_amount: result?.data?.payable_amount,
                paid_at: result?.data?.paid_at,
              },
            })
          }
          else {
            globalThis.location.href = `/payment-failure?order_id=${response.razorpay_order_id}`
          }
        }
        catch (error) {
          console.error('Payment verification failed:', error)
          globalThis.location.href = `/payment-failure?order_id=${response.razorpay_order_id}`
        }
        finally {
          // setGlobalLoading(false)
        }
      },
      // image: "/arsi_logo.png",
      key: rzpayKey,
      name: 'Aizawl Garbo',
      order_id: orderId,
      // prefill: {
      //   // contact: phoneNumber,
      //   // name,
      // },
      // method: "upi",
      // apps: ["google_pay", "phonepe", "bhim", "paytm", "sbi"],
      theme: {
        color: '#323232',
      },
    }

    const rzpay = new window.Razorpay(options)
    rzpay.open()

    rzpay.on('payment.failed', () => {
      console.error('razorpay on payment.failed')
      // void navigate("/payment-failure", {
      //   state: {
      //     order_id: orderId,
      //   },
      // })
      globalThis.location.href = `/payment-failure?order_id=${orderId}`
    })
  }
  catch (error) {
    console.error('Error creating order:', error)
  }
}
