'use client'

let isRazorpayLoaded = false
let razorpayScript: HTMLScriptElement | null = null
// let razorpayLoadPromise: Promise<boolean> | null = null;

export function loadRazorpayScript() {
  if (isRazorpayLoaded && razorpayScript) {
    return Promise.resolve(true)
  }

  if (!document) {
    return Promise.resolve(false)
  }

  return new Promise((resolve) => {
    const script = document.createElement('script')
    script.src = 'https://checkout.razorpay.com/v1/checkout.js'
    script.id = 'razorpay-checkout-js'

    script.addEventListener('load', () => {
      isRazorpayLoaded = true
      razorpayScript = script
      resolve(true)
    })
    script.addEventListener('error', () => {
      isRazorpayLoaded = false
      razorpayScript = null
      resolve(false)
    })

    document.head.appendChild(script)
  })
}

export function removeRazorpayScript() {
  if (razorpayScript && razorpayScript.parentNode) {
    razorpayScript.parentNode.removeChild(razorpayScript)
    razorpayScript = null
    isRazorpayLoaded = false
  }
}
