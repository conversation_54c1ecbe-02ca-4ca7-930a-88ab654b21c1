import { format } from 'date-fns'

export function formatDateRange(startDate: string, endDate: string): string {
  const start = new Date(startDate)
  const end = new Date(endDate)

  // Check if same month and year
  if (start.getMonth() === end.getMonth() && start.getFullYear() === end.getFullYear()) {
    return format(start, 'MMM yy')
  }

  // Different months/years - show range
  return `${format(start, 'do MMM yy')} - ${format(end, 'do MMM yy')}`
}
