import type { Customer } from '~/gql/graphql'
import z from 'zod'

export const addCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  address: z.string().min(1, 'Address is required'),
  // collection_days: z.array(z.enum(CollectionDay)).min(1, 'Collection days is required'),
  phone_number: z.string().min(10, 'Phone number must be 10 digits').max(10, 'Phone number must be 10 digits'),
  // subscription_status: z.enum(SubscriptionStatus),
  // customer_remarks: z.string().optional(),
  alternate_phone_number: z.string().optional(),
  // zone_id: z.string().min(1, 'Zone is required'),
  // plan_id: z.string().optional(),
  // period: z.number().optional(),
  // start_date: z.string().optional(),
})

export const updateCustomerSchema = z.object({
  id: z.string().min(1, 'ID is required'),
  name: z.string().optional(),
  address: z.string().optional(),
  phone_number: z.string().optional(),
  alternate_phone_number: z.string().optional(),
  // zone_id: z.string().optional(),
  // collection_days: z.array(z.enum(CollectionDay)).min(1, 'Collection days is required'),
  // plan_id: z.string().optional(),
  // period: z.number({ error: 'Please enter valid billing cycle' }).optional(),
  // subscription_status: z.enum(SubscriptionStatus).optional(),
  // customer_remarks: z.string().optional(),
  // start_date: z.string().optional(),
})

// export type SelectedCustomer = Pick<Customer, 'id' | 'name' | 'address' | 'phone_number' | 'zone' | 'verified_at' | 'collection_day' | 'alternate_phone_number' | 'current_plan_period' | 'current_plan_id' | 'remarks'>
export type SelectedCustomer = Pick<Customer, 'id' | 'name' | 'address' | 'phone_number' | 'alt_phone_number'>

export type AddCustomerType = z.infer<typeof addCustomerSchema>
export type UpdateCustomerType = z.infer<typeof updateCustomerSchema>
