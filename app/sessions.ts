import { createCookieSessionStorage } from 'react-router'

interface SessionData {
  role: string
  token: string
}

interface SessionFlashData {
  error: string
  successMessage: string
}

const { commitSession, destroySession, getSession }
  = createCookieSessionStorage<SessionData, SessionFlashData>({
    cookie: {
      httpOnly: true,
      name: '__aizawl_garbo',
      path: '/',
      secrets: ['a1zwalg3rb0'],
      // eslint-disable-next-line node/prefer-global/process
      secure: process.env.NODE_ENV === 'production',
    },
  })

export { commitSession, destroySession, getSession }
