import type { ResendPaymentLinkType } from '~/schema/resend-payment-link'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { RESEND_PAYMENT_LINK } from '../routes/_admin.admin_.customers_.$id/graphql'

export default function useResendPaymentLink() {
  const resendPaymentLink = useMutation({
    mutationFn: async (data: ResendPaymentLinkType) => {
      const client = await graphqlClient()
      return client.request({
        document: RESEND_PAYMENT_LINK,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success('Payment link resent successfully')
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { resendPaymentLink }
}
