import type { CustomerType, SubscriptionStatus } from '~/gql/graphql'
import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { parseAsInteger, parseAsString, useQueryState } from 'nuqs'
import { useDebounce } from 'use-debounce'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_CUSTOMERS } from '../routes/_admin.admin_.customers/graphql'

interface Props {
  customerType: CustomerType
}

export default function useGetCustomers({ customerType }: Props) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))
  const [name, setName] = useQueryState('name', parseAsString.withDefault(''))
  const [subscriptionStatus, setSubscriptionStatus] = useQueryState('subscription_status', parseAsString.withDefault(''))
  const [zoneId, setZoneId] = useQueryState('zone_id', parseAsString.withDefault(''))
  const [planId, setPlanId] = useQueryState('plan_id', parseAsString.withDefault(''))
  const [startDate, setStartDate] = useQueryState('start_date', parseAsString.withDefault(''))
  const [endDate, setEndDate] = useQueryState('end_date', parseAsString.withDefault(''))
  const [phoneNumber, setPhoneNumber] = useQueryState('phone_number', parseAsString.withDefault(''))
  // const [customerType, setCustomerType] = useQueryState('customer_type', parseAsString.withDefault(''))

  const [debouncedName] = useDebounce(name, 500)
  const [debouncedPhoneNumber] = useDebounce(phoneNumber, 500)

  const handlePage = (page: number) => {
    setPage(page)
  }

  const handleSearch = (params: {
    name?: string
    zoneId?: string
    subscriptionStatus?: string
    type?: string
    paymentMode?: string
    month?: string
    startDate?: string
    endDate?: string
    phoneNumber?: string
    // customerType?: string
  }) => {
    if (params.name !== undefined)
      setName(params.name)
    if (params.zoneId !== undefined)
      setZoneId(params.zoneId)
    if (params.subscriptionStatus !== undefined)
      setSubscriptionStatus(params.subscriptionStatus)
    if (params.type !== undefined)
      setPlanId(params.type)
    if (params.startDate !== undefined)
      setStartDate(params.startDate)
    if (params.endDate !== undefined)
      setEndDate(params.endDate)
    if (params.phoneNumber !== undefined)
      setPhoneNumber(params.phoneNumber)
    // if (params.customerType !== undefined)
    //   setCustomerType(params.customerType)
    setPage(1) // Reset to first page when searching
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-customers', page, debouncedName, zoneId, subscriptionStatus, planId, startDate, endDate, debouncedPhoneNumber, customerType],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_CUSTOMERS,
        variables: {
          first: 15,
          page,
          name: debouncedName || undefined,
          subscription_status: subscriptionStatus as SubscriptionStatus || undefined,
          plan_id: planId || undefined,
          zone_id: zoneId || undefined,
          start_date: startDate ? format(new Date(startDate), 'yyyy-MM-dd hh:mm:ss') : undefined,
          end_date: endDate ? format(new Date(endDate), 'yyyy-MM-dd hh:mm:ss') : undefined,
          phone_number: debouncedPhoneNumber || undefined,
          customer_type: customerType,
        },
      })
    },
  })

  const lastPage = data?.getCustomers?.paginator_info?.last_page || 1

  return {
    data,
    isLoading,
    isError,
    handlePage,
    handleSearch,
    page,
    filters: {
      name,
      zoneId,
      subscriptionStatus,
      type: planId,
      startDate,
      endDate,
      phoneNumber,
    },
    lastPage,
  }
}
