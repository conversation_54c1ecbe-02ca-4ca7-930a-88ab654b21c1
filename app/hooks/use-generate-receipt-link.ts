import type { GenerateReceiptLinkType } from '~/schema/generate-receipt-link'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { GENERATE_RECEIPT_LINK } from '../routes/_admin.admin_.customers_.$id/graphql'

export default function useGenerateReceiptLink() {
  const generateReceiptLink = useMutation({
    mutationFn: async (data: GenerateReceiptLinkType) => {
      const client = await graphqlClient()
      return client.request({
        document: GENERATE_RECEIPT_LINK,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success('Receipt link generated successfully')
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { generateReceiptLink }
}
