import type { MarkAsPaidType } from '~/schema/mark-as-paid'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { format } from 'date-fns'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { MARK_AS_PAID } from '../routes/_admin.admin_.customers_.$id/graphql'

export default function useMarkAsPaid() {
  const queryClient = useQueryClient()
  const markAsPaid = useMutation({
    mutationFn: async (data: MarkAsPaidType) => {
      const client = await graphqlClient()
      return client.request({
        document: MARK_AS_PAID,
        variables: {
          ...data,
          payment_date: data.payment_date ? format(new Date(data.payment_date), 'yyyy-MM-dd hh:mm:ss') : undefined,
        },
      })
    },
    onSuccess: () => {
      toast.success('Marked as paid')
      queryClient.invalidateQueries({
        queryKey: ['customer-subscription-history'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { markAsPaid }
}
