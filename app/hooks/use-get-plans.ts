import { useQuery } from '@tanstack/react-query'
import { GET_PLANS } from '~/graphql/queries/get-plans'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetPlans() {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['plans'],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_PLANS,
      })
    },
  })

  return { data, isLoading, isError }
}
