import { createFormHook, createFormHookContexts } from '@tanstack/react-form'
import FormMessage from '~/components/common/form-message'
import { Button } from '~/components/ui/button'
import { Checkbox } from '~/components/ui/checkbox'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Textarea } from '~/components/ui/textarea'
import { cn } from '~/lib/utils'

const { fieldContext, useFieldContext, formContext, useFormContext }
  = createFormHookContexts()

export function InputField({ label, type, placeholder, multiple }: { label: string, type?: string, placeholder?: string, multiple?: boolean }) {
  const field = useFieldContext<string>()
  return (
    <Label className="flex flex-col items-start gap-y-2">
      <div>{label}</div>
      <Input
        value={field.state.value}
        onChange={e => field.handleChange(e.target.value)}
        placeholder={placeholder}
        className={cn('bg-white', {
          block: type === 'date',
        })}
        type={type}
        multiple={multiple}
      />
      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </Label>
  )
}

export function TextareaField({ label, placeholder }: { label: string, placeholder?: string }) {
  const field = useFieldContext<string>()
  return (
    <Label className="flex flex-col items-start gap-y-2">
      <div>{label}</div>
      <Textarea
        value={field.state.value}
        onChange={e => field.handleChange(e.target.value)}
        placeholder={placeholder}
        className="bg-white"
      />
      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </Label>
  )
}

export function NumberField({ label, placeholder }: { label: string, type?: string, placeholder?: string }) {
  const field = useFieldContext<number | undefined | ''>()
  return (
    <Label className="flex flex-col items-start gap-y-2">
      <div>{label}</div>
      <Input
        value={field.state.value}
        onChange={(e) => {
          const value = e.target.value ? Number.parseInt(e.target.value, 10) : ''
          field.handleChange(value)
        }}
        placeholder={placeholder}
        className="bg-white"
      />
      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </Label>
  )
}

export function CheckboxField({ label }: { label: string }) {
  const field = useFieldContext<boolean>()
  return (
    <Label className="flex items-center gap-x-2">
      <Checkbox
        className="bg-white"
        checked={field.state.value === true}
        onCheckedChange={checked => field.handleChange(!!checked)}
      />
      <div>{label}</div>
      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </Label>
  )
}

export function CheckboxInputField({
  label,
  value,
  disabled,
}: {
  label: string
  value: string
  disabled?: boolean
}) {
  const field = useFieldContext<string[]>()

  const isChecked = field.state.value.includes(value)

  const handleChange = (checked: boolean) => {
    if (checked) {
      field.handleChange([...field.state.value, value])
    }
    else {
      field.handleChange(field.state.value.filter(item => item !== value))
    }
  }

  return (
    <label
      className={cn(
        `
          col-span-1 flex cursor-pointer justify-center rounded-md border
          bg-white p-2 text-sm transition-colors
          focus-within:ring-2 focus-within:ring-offset-2
        `,
        {
          'bg-primary text-primary-foreground': isChecked,
          'cursor-not-allowed opacity-50': disabled,
        },
      )}
    >
      <input
        className="absolute opacity-0"
        disabled={disabled}
        onChange={e => handleChange(e.target.checked)}
        type="checkbox"
        checked={isChecked}
        value={value}
      />
      <div className="flex size-full flex-col items-center justify-center">
        <span>{label}</span>
      </div>
    </label>
  )
}

export function MobileInputField({ label }: { label: string }) {
  const field = useFieldContext<string>()
  return (
    <Label className="relative flex w-full flex-col items-start gap-y-2">
      <div>{label}</div>
      <Input
        value={field.state.value}
        onChange={e => field.handleChange(e.target.value)}
        type="tel"
        maxLength={10}
      />
      <div
        className={cn('w-full text-right text-xs opacity-50', {
          'absolute right-1 bottom-0': field.state.meta.errors.length > 0,
        })}
      >
        {field.state.value?.length || 0}
        /10
      </div>

      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </Label>
  )
}

export function SubmitButton({ label }: { label: string }) {
  const form = useFormContext()
  return (
    <form.Subscribe selector={state => state.isSubmitting}>
      {isSubmitting => <Button isLoading={isSubmitting}>{label}</Button>}
    </form.Subscribe>
  )
}

export const { useAppForm, withForm } = createFormHook({
  fieldComponents: {
    InputField,
    MobileInputField,
    CheckboxField,
    CheckboxInputField,
    NumberField,
    TextareaField,
  },
  formComponents: {
    SubmitButton,
  },
  fieldContext,
  formContext,
})
