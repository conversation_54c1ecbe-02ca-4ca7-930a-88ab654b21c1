import { useMutation } from '@tanstack/react-query'
import { ADMIN_LOGIN } from '~/graphql/mutation/admin-login'
import { graphqlClient } from '~/lib/graphql-client'

export default function useAdminLogin() {
  const adminLogin = useMutation({
    mutationFn: async ({ username, password }: { username: string, password: string }) => {
      const client = await graphqlClient()
      return client.request({
        document: ADMIN_LOGIN,
        variables: {
          username,
          password,
        },
      })
    },
  })

  return { adminLogin }
}
