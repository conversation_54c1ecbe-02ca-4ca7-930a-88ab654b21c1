import type { AddCustomerType, UpdateCustomerType } from '~/lib/customer-schema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { DELETE_CUSTOMER } from '~/graphql/mutation/delete-customer'
import { UPDATE_CUSTOMER } from '~/graphql/mutation/update-customer'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { ADD_CUSTOMER } from '../routes/_admin.admin_.customers/graphql'

export default function useCustomerMutations() {
  const queryClient = useQueryClient()

  const addCustomer = useMutation({
    mutationFn: async (data: AddCustomerType) => {
      const client = await graphqlClient()
      return client.request({
        document: ADD_CUSTOMER,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success('Customer added successfully')
      queryClient.invalidateQueries({
        queryKey: ['get-customers'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-collection-schedule'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  const deleteCustomer = useMutation({
    mutationFn: async (id: string) => {
      const client = await graphqlClient()
      return client.request({
        document: DELETE_CUSTOMER,
        variables: {
          id,
        },
      })
    },
    onSuccess: () => {
      toast.success('Customer deleted successfully')
      queryClient.invalidateQueries({
        queryKey: ['get-customers'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  const updateCustomer = useMutation({
    mutationFn: async (data: UpdateCustomerType) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_CUSTOMER,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success('Customer updated successfully')
      queryClient.invalidateQueries({
        queryKey: ['get-customers'],
      },
      )
      queryClient.invalidateQueries({
        queryKey: ['get-collection-schedule'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-payment-records'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { addCustomer, deleteCustomer, updateCustomer }
}
