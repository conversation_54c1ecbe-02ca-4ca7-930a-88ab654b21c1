import { useQuery } from '@tanstack/react-query'
import { GET_ZONES_AND_PLANS } from '~/graphql/queries/get-zones-and-plans'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetZonesAndPlans() {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['zones-and-plans'],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_ZONES_AND_PLANS,
      })
    },
  })

  return { data, isLoading, isError }
}
