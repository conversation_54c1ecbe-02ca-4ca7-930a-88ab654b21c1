import type { Route } from './+types/root'

import { MutationCache, QueryCache, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ClientError } from 'graphql-request'

import { NuqsAdapter } from 'nuqs/adapters/react-router/v7'
import { useState } from 'react'
import {
  isRouteErrorResponse,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from 'react-router'
import { Toaster } from 'sonner'
import './app.css'

export const links: Route.LinksFunction = () => [
  { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
  {
    rel: 'preconnect',
    href: 'https://fonts.gstatic.com',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'stylesheet',
    href: 'https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap',
  },
]

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}

export default function App() {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000,
            retry: (failureCount, error) => {
              if (error instanceof ClientError) {
                if (error?.response?.status === 419) {
                  return false
                }
                if (error?.response?.status === 503) {
                  return false
                }
                return failureCount < 2
              }

              return false
            },
          },
        },
        mutationCache: new MutationCache({
          onError: async (error) => {
            if (error instanceof ClientError) {
              if (error?.response?.status === 503) {
                globalThis.location.href = '/maintenance-mode'
              }
              error?.response?.errors?.map((error) => {
                // alert("unauthenticated error")
                if (error.message === 'Unauthenticated.') {
                  globalThis.location.href = '/login'
                }
                return null
              })
            }
          },
        }),
        queryCache: new QueryCache({
          onError: async (error) => {
            if (error instanceof ClientError) {
              if (error?.response?.status === 503) {
                globalThis.location.href = '/maintenance-mode'
              }
              error?.response?.errors?.map((error) => {
                // alert("unauthenticated error")
                if (error.message === 'Unauthenticated.') {
                  globalThis.location.href = '/login'
                }

                return null
              })
            }
          },
        }),
      }),
  )

  return (
    <QueryClientProvider client={queryClient}>
      <Toaster />
      <NuqsAdapter>
        <Outlet />
      </NuqsAdapter>
    </QueryClientProvider>
  )
}

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  let message = 'Oops!'
  let details = 'An unexpected error occurred.'
  let stack: string | undefined

  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? '404' : 'Error'

    details
      = error.status === 404
        ? 'The requested page could not be found.'
        : error.data || error.statusText || details
  }
  else if (import.meta.env.DEV && error && error instanceof Error) {
    details = error.message
    stack = error.stack
  }

  return (
    <main className="container mx-auto p-4 pt-16">
      <h1>{message}</h1>
      <p>{details}</p>
      {stack && (
        <pre className="w-full overflow-x-auto p-4">
          <code>{stack}</code>
        </pre>
      )}
    </main>
  )
}
