import { useNavigate } from 'react-router'
import ChevronLeftIcon from '../icons/chevron-left-Icon'

function BackButton() {
  const navigate = useNavigate()
  return (
    <div>
      <button
        type="button"
        className="-ml-2 flex items-center"
        onClick={async () => {
          navigate(-1)
        }}
      >
        <ChevronLeftIcon className="size-8" />
        {' '}
        Back
      </button>
    </div>
  )
}

export default BackButton
