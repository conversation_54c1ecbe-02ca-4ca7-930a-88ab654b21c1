import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip'

interface Props {
  message: string
  children: React.ReactNode
  className?: string
}

export default function AppTooltip({ message, children, className }: Props) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent className={className}>
        {message}
      </TooltipContent>
    </Tooltip>
  )
}
