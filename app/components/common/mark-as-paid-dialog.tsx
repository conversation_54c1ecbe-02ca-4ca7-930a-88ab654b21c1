import type { MarkAsPaidType } from '~/schema/mark-as-paid'
import { format } from 'date-fns'
import FormMessage from '~/components/common/form-message'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { PaymentMode } from '~/gql/graphql'
import { useAppForm } from '~/hooks/form'
import useMarkAsPaid from '~/hooks/use-mark-as-paid'
import { markAsPaidSchema } from '~/schema/mark-as-paid'

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  id: string
}

export default function MarkAsPaidDialog({ id, isOpen, toggle }: Props) {
  const { markAsPaid } = useMarkAsPaid()

  const form = useAppForm({
    defaultValues: {
      subscription_id: id,
      payment_mode: '' as unknown as PaymentMode,
      payment_date: format(new Date(), 'yyyy-MM-dd'),
      remarks: '',
    } as MarkAsPaidType,
    validators: {
      onSubmit: markAsPaidSchema,
    },
    onSubmit: async ({ value }) => {
      await markAsPaid.mutateAsync(value, {
        onSuccess: () => {
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Mark as paid
          </DialogTitle>
          <DialogDescription>
            Select mode of payment and date
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="space-y-4"
        >
          <form.AppField
            name="payment_mode"
            children={field => (
              <>
                <Label className="flex flex-col items-start gap-y-2">
                  <div>Payment Mode</div>
                  <Select
                    value={field.state.value}
                    onValueChange={(e) => {
                      field.handleChange(e as PaymentMode)
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select payment mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={PaymentMode.Cash}>Cash</SelectItem>
                      <SelectItem value={PaymentMode.Cheque}>Cheque</SelectItem>
                      <SelectItem value={PaymentMode.Upi}>UPI</SelectItem>
                      <SelectItem value={PaymentMode.Card}>Card</SelectItem>
                      <SelectItem value={PaymentMode.Paymentlink}>Payment Link</SelectItem>

                    </SelectContent>
                  </Select>
                  <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                </Label>
              </>
            )}
          />
          <form.AppField
            name="payment_date"
            children={field => <field.InputField type="date" label="Payment date" />}
          />
          <form.AppField
            name="remarks"
            children={field => <field.TextareaField label="Remarks" />}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Mark as paid" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
