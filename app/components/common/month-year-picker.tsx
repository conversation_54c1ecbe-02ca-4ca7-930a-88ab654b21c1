import { useState } from 'react'
import { months } from '~/lib/constants'
import { generateYearOptions } from '~/lib/generate-year-options'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'

interface Props {
  value?: string
  onChange: (value: string) => void
}

function MonthYearPicker({ value, onChange }: Props) {
  const [month, setMonth] = useState(value ? new Date(value).getMonth() : '')
  const [year, setYear] = useState(value ? new Date(value).getFullYear() : '')

  const yearOptions = generateYearOptions()
  yearOptions.push(new Date().getFullYear() + 1)

  const handleMonthChange = (e: string) => {
    const selectedMonth = Number.parseInt(e)
    setMonth(selectedMonth)

    if (!year) {
      setYear(new Date().getFullYear())
    }

    const selectedYear = typeof year === 'number' ? year : new Date().getFullYear()
    const newDate = new Date(selectedYear, selectedMonth, 1)

    onChange(newDate.toISOString().split('T')[0])
  }

  const handleYearChange = (e: string) => {
    const selectedYear = Number.parseInt(e)
    setYear(selectedYear)

    if (month === '' || month === null || month === undefined) {
      setMonth(new Date().getMonth())
    }

    const selectedMonth = typeof month === 'number' ? month : new Date().getMonth()
    const newDate = new Date(selectedYear, selectedMonth, 1)
    onChange(newDate.toISOString().split('T')[0])
  }

  return (
    <div className="flex w-full gap-x-4">
      <Select value={month.toString()} onValueChange={handleMonthChange}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select month" />
        </SelectTrigger>
        <SelectContent>
          {months.map(m => (
            <SelectItem key={m.value} value={m.value.toString()}>{m.label}</SelectItem>
          ))}

        </SelectContent>
      </Select>
      <Select value={year.toString()} onValueChange={handleYearChange}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select year" />
        </SelectTrigger>
        <SelectContent>
          {yearOptions.map(year => (
            <SelectItem key={year} value={(year).toString()}>{year}</SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

export default MonthYearPicker
