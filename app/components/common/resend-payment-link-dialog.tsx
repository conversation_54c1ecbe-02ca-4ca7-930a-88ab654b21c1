import type { ResendPaymentLinkType } from '~/schema/resend-payment-link'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { useAppForm } from '~/hooks/form'
import useResendPaymentLink from '~/hooks/use-resend-payment-link'
import { resendPaymentLinkSchema } from '~/schema/resend-payment-link'

interface Props {
  phoneNumber: string
  subscriptionId: string
  customerId: string
  payableAmount: number
  isOpen: boolean
  toggle: (open: boolean) => void
}

export default function ResendPaymentLinkDialog({ phoneNumber, subscriptionId, isOpen, toggle, customerId, payableAmount }: Props) {
  const { resendPaymentLink } = useResendPaymentLink()
  const form = useAppForm({
    defaultValues: {
      phone_number: phoneNumber,
      subscription_id: subscriptionId,
      customer_id: customerId,
      payable_amount: payableAmount,
    } as ResendPaymentLinkType,
    validators: {
      onSubmit: resendPaymentLinkSchema,
    },
    onSubmit: async ({ value }) => {
      await resendPaymentLink.mutateAsync(
        value,
        {
          onSuccess: () => {
            toggle(false)
          },
        },
      )
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Resend Payment Link
          </DialogTitle>
          <DialogDescription>
            Recipient will receive payment reminder and link via Whatsapp
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="space-y-4"
        >
          <form.AppField
            name="phone_number"
            children={field => <field.MobileInputField label="Phone Number" />}
          />
          <form.AppField
            name="payable_amount"
            children={field => <field.NumberField label="Payable Amount" />}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Resend Payment Link" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
