import type { AddCustomerType } from '~/lib/customer-schema'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import useCustomerMutations from '~/hooks/use-customer-mutations'
import { addCustomerSchema } from '~/lib/customer-schema'

export default function AddCustomerDialog() {
  const { addCustomer } = useCustomerMutations()

  const { isOpen, toggle } = useBoolean()

  const form = useAppForm({
    defaultValues: {
      name: '',
      address: '',
      phone_number: '',
      alternate_phone_number: '',
    } as AddCustomerType,
    validators: {
      onSubmit: addCustomerSchema,
    },
    onSubmit: async ({ value }) => {
      await addCustomer.mutateAsync(value, {
        onSuccess: () => {
          form.reset()
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>
        <div className="flex justify-end">
          <Button variant="outline">
            Add Customer
          </Button>
        </div>
      </DialogTrigger>
      <DialogContent className="w-full min-w-4xl">
        <DialogHeader>
          <DialogTitle>
            Add new customer
          </DialogTitle>
          <DialogDescription>
            Enter customer details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="grid grid-cols-2 gap-x-8 gap-y-4"
        >
          <div className="col-span-1">
            <form.AppField
              name="name"
              children={field => <field.InputField label="Name" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="address"
              children={field => <field.InputField label="Address" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="phone_number"
              children={field => <field.MobileInputField label="Phone Number" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="alternate_phone_number"
              children={field => <field.MobileInputField label="Alternate phone number" />}
            />
          </div>
          <DialogFooter className="col-span-2 mt-2">
            <form.AppForm>
              <form.SubmitButton label="Add Customer" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
