import type { Plan, Zone } from '~/gql/graphql'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { CustomerType, SubscriptionStatus } from '~/gql/graphql'
import useGetCustomers from '~/hooks/use-get-customers'

interface Props {
  zones: Zone[]
  plans: Plan[]
}

export default function CustomerQueries({ zones, plans }: Props) {
  const { filters, handleSearch } = useGetCustomers({ customerType: CustomerType.Approved })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSearch({ [e.target.name]: e.target.value })
  }

  return (
    <div className="grid grid-cols-12 gap-4">
      <Label className="col-span-3 flex flex-col items-start gap-y-2">
        <div>Name</div>
        <Input placeholder="Search by name" value={filters.name} name="name" onChange={handleInputChange} />
      </Label>
      <Label className="col-span-3 flex flex-col items-start gap-y-2">
        <div>Phone number</div>
        <Input placeholder="Search by phone number" name="phoneNumber" value={filters.phoneNumber} onChange={handleInputChange} />
      </Label>
      <Label className="col-span-3 flex flex-col items-start gap-y-2">
        <div>Start Date</div>
        <Input
          name="startDate"
          placeholder="Search by start date"
          className="block"
          type="date"
          value={filters.startDate}
          onChange={handleInputChange}
        />
      </Label>
      <Label className="col-span-3 flex flex-col items-start gap-y-2">
        <div>End Date</div>
        <Input name="endDate" placeholder="Search by end date" className="block" type="date" value={filters.endDate} onChange={handleInputChange} />
      </Label>
      <Label className="col-span-3 flex flex-col items-start gap-y-2">
        <div>Zone</div>
        <Select value={filters.zoneId} onValueChange={value => handleSearch({ zoneId: value === 'All' ? '' : value })}>
          <SelectTrigger className="w-full bg-white">
            <SelectValue placeholder="Search by zone" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All zones</SelectItem>
            {zones.map((zone) => {
              return <SelectItem key={zone.id} value={zone.id}>{zone.name}</SelectItem>
            })}
          </SelectContent>
        </Select>
      </Label>
      <Label className="col-span-3 flex flex-col items-start gap-y-2">
        <div>Plan</div>
        <Select value={filters.type} onValueChange={value => handleSearch({ type: value === 'All' ? '' : value })}>
          <SelectTrigger className="w-full bg-white">
            <SelectValue placeholder="Search by plan" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All plans</SelectItem>
            {plans.map((plan) => {
              return <SelectItem key={plan.id} value={plan.id}>{plan.name}</SelectItem>
            })}
          </SelectContent>
        </Select>
      </Label>
      <Label className="col-span-3 flex flex-col items-start gap-y-2">
        <div>Subscription Status</div>
        <Select value={filters.subscriptionStatus} onValueChange={value => handleSearch({ subscriptionStatus: value === 'All' ? '' : value })}>
          <SelectTrigger className="w-full bg-white">
            <SelectValue placeholder="Search by subscription status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All statuses</SelectItem>
            <SelectItem value={SubscriptionStatus.Active}>Active</SelectItem>
            <SelectItem value={SubscriptionStatus.Inactive}>Inactive</SelectItem>
          </SelectContent>
        </Select>
      </Label>
    </div>
  )
}
