import type { SelectedCustomer, UpdateCustomerType } from '~/lib/customer-schema'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { useAppForm } from '~/hooks/form'
import useCustomerMutations from '~/hooks/use-customer-mutations'
import { updateCustomerSchema } from '~/lib/customer-schema'

interface Props {
  isOpen: boolean
  handleOpenChange: (open: boolean) => void
  customer: SelectedCustomer
}

export default function UpdateCustomerDialog({ isOpen, handleOpenChange, customer }: Props) {
  const { updateCustomer } = useCustomerMutations()

  const form = useAppForm({
    defaultValues: {
      id: customer.id,
      name: customer.name,
      address: customer.address,
      phone_number: customer.phone_number,
      alternate_phone_number: customer.alt_phone_number || '',
    } as UpdateCustomerType,
    validators: {
      onSubmit: updateCustomerSchema,
    },
    onSubmit: async ({ value }) => {
      await updateCustomer.mutateAsync(value, {
        onSuccess: () => {
          handleOpenChange(false)
          form.reset()
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="w-full min-w-4xl">
        <DialogHeader>
          <DialogTitle>
            Update customer
          </DialogTitle>
          <DialogDescription>
            Enter customer details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="grid grid-cols-2 gap-x-8 gap-y-4"
        >
          <div className="col-span-1">

            <form.AppField
              name="name"
              children={field => <field.InputField label="Name" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="address"
              children={field => <field.InputField label="Address" />}
            />

          </div>
          <div className="col-span-1">
            <form.AppField
              name="phone_number"
              children={field => <field.MobileInputField label="Phone Number" />}
            />

          </div>
          <div className="col-span-1">
            <form.AppField
              name="alternate_phone_number"
              children={field => <field.MobileInputField label="Alternate Phone Number" />}
            />

          </div>

          <DialogFooter className="col-span-2">
            <form.AppForm>
              <form.SubmitButton label="Update Customer" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
