import type { Route } from './+types/route'
import { useLoaderData } from 'react-router'
import PageHeader from '~/components/common/page-header'
import { GET_ZONES_AND_PLANS } from '~/graphql/queries/get-zones-and-plans'
import { graphqlClient } from '~/lib/graphql-client'
import { getSession } from '~/sessions'
import AddCustomerDialog from './add-customer-dialog'
import CustomerQueries from './customer-queries'
import CustomerTable from './customer-table'

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get('Cookie'))
  const token = session.get('token')
  const client = await graphqlClient({ token })

  const data = await client.request({
    document: GET_ZONES_AND_PLANS,
  })

  const zones = data.getZones || []
  const plans = data.getPlans || []

  return {
    zones,
    plans,
  }
}

export default function Customers() {
  const { zones, plans } = useLoaderData<typeof loader>()

  return (
    <div className="flex grow flex-col gap-4">
      <PageHeader title="Customers" />
      <CustomerQueries zones={zones} plans={plans} />
      <CustomerTable />
      <AddCustomerDialog />
    </div>
  )
}
