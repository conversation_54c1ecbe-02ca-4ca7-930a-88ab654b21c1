import type { Plan, Zone } from '~/gql/graphql'
import type { AddCustomerType } from '~/lib/customer-schema'
import FormMessage from '~/components/common/form-message'
import MonthYearPicker from '~/components/common/month-year-picker'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { SubscriptionStatus } from '~/gql/graphql'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import useCustomerMutations from '~/hooks/use-customer-mutations'
import { addCustomerSchema } from '~/lib/customer-schema'

interface Props {
  zones: Zone[]
  plans: Plan[]
}

export default function AddCustomerDialog({ zones, plans }: Props) {
  const { addCustomer } = useCustomerMutations()

  const { isOpen, toggle } = useBoolean()

  const form = useAppForm({
    defaultValues: {
      name: '',
      address: '',
      phone_number: '',
      alternate_phone_number: '',
      zone_id: '',
      plan_id: '',
      customer_remarks: '',
      period: undefined,
      collection_days: [],
      start_date: '',
      subscription_status: '' as unknown as SubscriptionStatus,
    } as AddCustomerType,
    validators: {
      onSubmit: addCustomerSchema,
    },
    onSubmit: async ({ value }) => {
      await addCustomer.mutateAsync(value, {
        onSuccess: () => {
          form.reset()
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>
        <div className="flex justify-end">
          <Button variant="outline">
            Add Customer
          </Button>
        </div>
      </DialogTrigger>
      <DialogContent className="w-full min-w-4xl">
        <DialogHeader>
          <DialogTitle>
            Add new customer
          </DialogTitle>
          <DialogDescription>
            Enter customer details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="grid grid-cols-2 gap-x-8 gap-y-4"
        >
          <div className="col-span-1">
            <form.AppField
              name="name"
              children={field => <field.InputField label="Name" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="address"
              children={field => <field.InputField label="Address" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="phone_number"
              children={field => <field.MobileInputField label="Phone Number" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="alternate_phone_number"
              children={field => <field.MobileInputField label="Alternate phone number" />}
            />
          </div>
          <div className="col-span-2 my-2 w-full border" />
          <div className="col-span-1">
            <form.AppField
              name="period"
              children={field => <field.NumberField label="Billing cycle" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="start_date"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>
                      Start Date
                    </div>
                    <MonthYearPicker value={field.state.value} onChange={field.handleChange} />
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="plan_id"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Subscription Plan</div>
                    <Select value={field.state.value} onValueChange={field.handleChange}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {plans.map((plan) => {
                          return <SelectItem key={plan.id} value={plan.id}>{plan.name}</SelectItem>
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="zone_id"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Zone</div>
                    <Select value={field.state.value} onValueChange={field.handleChange}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {zones.map((zone) => {
                          return <SelectItem key={zone.id} value={zone.id}>{zone.name}</SelectItem>
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="subscription_status"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Subscription Status</div>
                    <Select
                      value={field.state.value}
                      onValueChange={(e) => {
                        field.handleChange(e as SubscriptionStatus)
                      }}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={SubscriptionStatus.Active}>Active</SelectItem>
                        <SelectItem value={SubscriptionStatus.Inactive}>Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>
          <div className="col-span-2">
            <form.AppField
              name="customer_remarks"
              children={field => <field.TextareaField label="Remarks" />}
            />
          </div>
          <div className="col-span-2">
            <form.AppField
              name="collection_days"
              children={field => (
                <div className="flex flex-col gap-y-2">
                  <Label>Collection Days</Label>
                  <div className="grid grid-cols-7 gap-2">
                    <field.CheckboxInputField
                      label="Mon"
                      value="MONDAY"
                    />
                    <field.CheckboxInputField
                      label="Tue"
                      value="TUESDAY"
                    />
                    <field.CheckboxInputField
                      label="Wed"
                      value="WEDNESDAY"
                    />
                    <field.CheckboxInputField
                      label="Thu"
                      value="THURSDAY"
                    />
                    <field.CheckboxInputField
                      label="Fri"
                      value="FRIDAY"
                    />
                    <field.CheckboxInputField
                      label="Sat"
                      value="SATURDAY"
                    />
                    <field.CheckboxInputField
                      label="Sun"
                      value="SUNDAY"
                    />
                  </div>
                  <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                </div>
              )}
            />
          </div>
          <DialogFooter className="col-span-2 mt-2">
            <form.AppForm>
              <form.SubmitButton label="Add Customer" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
