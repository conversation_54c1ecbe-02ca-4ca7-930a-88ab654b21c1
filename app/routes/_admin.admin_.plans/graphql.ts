import { graphql } from '~/gql'

export const ADD_PLAN = graphql(`
  mutation AddPlan(
    $name: String!
    $price: Float!
    $remarks: String
    $is_internal: Boolean!
    $category: PlanCategory!
  ) {
    addPlan(name: $name, price: $price, remarks: $remarks, is_internal: $is_internal, category: $category)
  }
`)

export const UPDATE_PLAN = graphql(`
  mutation UpdatePlan(
    $id: ID!
    $name: String
    $price: Float
    $remarks: String
    $is_internal: Boolean
    $category: PlanCategory
  ) {
    updatePlan(id: $id, name: $name, price: $price, remarks: $remarks, is_internal: $is_internal, category: $category)
  }
`)

export const DELETE_PLAN = graphql(`
  mutation DeletePlan(
    $id: ID!
  ) {
    deletePlan(id: $id)
  }
`)
