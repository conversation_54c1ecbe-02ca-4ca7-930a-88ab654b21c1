import type { AddPlanType } from './schema'
import FormMessage from '~/components/common/form-message'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { PlanCategory } from '~/gql/graphql'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import { addPlanSchema } from './schema'
import usePlansMutations from './use-plans-mutations'

export default function AddPlanDialog() {
  const { addPlan } = usePlansMutations()
  const { isOpen, toggle } = useBoolean()

  const form = useAppForm({
    defaultValues: {
      name: '',
      price: '' as unknown as number,
      remarks: '',
      is_internal: false,
      category: '' as unknown as PlanCategory,
    } as AddPlanType,
    validators: {
      onSubmit: addPlanSchema,
    },
    onSubmit: async ({ value }) => {
      await addPlan.mutateAsync(value, {
        onSuccess: () => {
          form.reset()
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>
        <div className="flex justify-end">
          <Button variant="outline">
            Add Plan
          </Button>
        </div>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Add new plan
          </DialogTitle>
          <DialogDescription>
            Enter plan details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-y-4"
        >
          <form.AppField
            name="name"
            children={field => <field.InputField label="Name" />}
          />
          <form.AppField
            name="price"
            children={field => <field.NumberField label="Price" />}
          />
          <form.AppField
            name="remarks"
            children={field => <field.InputField label="Remarks" />}
          />
          <form.AppField
            name="category"
            children={field => (
              <>
                <Label className="flex flex-col items-start gap-y-2">
                  <div>Category</div>
                  <Select value={field.state.value} onValueChange={e => field.handleChange(e as PlanCategory)}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={PlanCategory.Commercial}>Commercial</SelectItem>
                      <SelectItem value={PlanCategory.Domestic}>Domestic</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                </Label>
              </>
            )}
          />
          <form.AppField
            name="is_internal"
            children={field => <field.CheckboxField label="Is internal?" />}
          />
          <form.AppForm>
            <form.SubmitButton label="Add Plan" />
          </form.AppForm>
        </form>
      </DialogContent>
    </Dialog>
  )
}
