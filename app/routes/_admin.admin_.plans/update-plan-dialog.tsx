import type { UpdatePlanType } from './schema'
import type { Plan } from '~/gql/graphql'
import FormMessage from '~/components/common/form-message'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { PlanCategory } from '~/gql/graphql'
import { useAppForm } from '~/hooks/form'
import { updatePlanSchema } from './schema'
import usePlansMutations from './use-plans-mutations'

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  plan: Plan
}

export default function UpdatePlanDialog({ isOpen, toggle, plan }: Props) {
  const { updatePlan } = usePlansMutations()

  const form = useAppForm({
    defaultValues: {
      id: plan.id,
      name: plan.name,
      price: plan.price,
      remarks: plan.remarks || '',
      is_internal: plan.is_internal,
      category: plan.category === 'commercial' ? PlanCategory.Commercial : PlanCategory.Domestic,
    } as UpdatePlanType,
    validators: {
      onSubmit: updatePlanSchema,
    },
    onSubmit: async ({ value }) => {
      await updatePlan.mutateAsync(value, {
        onSuccess: () => {
          toggle(false)
          form.reset()
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Update Plan
          </DialogTitle>
          <DialogDescription>
            Enter updated plan details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-y-4"
        >
          <form.AppField
            name="name"
            children={field => <field.InputField label="Name" />}
          />
          <form.AppField
            name="price"
            children={field => <field.NumberField label="Price" />}
          />
          <form.AppField
            name="remarks"
            children={field => <field.InputField label="Remarks" />}
          />
          <form.AppField
            name="category"
            children={field => (
              <>
                <Label className="flex flex-col items-start gap-y-2">
                  <div>Category</div>
                  <Select value={field.state.value} onValueChange={e => field.handleChange(e as PlanCategory)}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={PlanCategory.Commercial}>Commercial</SelectItem>
                      <SelectItem value={PlanCategory.Domestic}>Domestic</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                </Label>
              </>
            )}
          />
          <form.AppField
            name="is_internal"
            children={field => <field.CheckboxField label="Is internal?" />}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Update Plan" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
