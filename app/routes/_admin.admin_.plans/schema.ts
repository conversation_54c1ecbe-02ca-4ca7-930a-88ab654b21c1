import z from 'zod'
import { PlanCategory } from '~/gql/graphql'

export const addPlanSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  price: z.number().min(1, 'Price is required'),
  remarks: z.string().optional(),
  is_internal: z.boolean(),
  category: z.enum(PlanCategory),
})

export const updatePlanSchema = z.object({
  id: z.string().min(1, 'ID is required'),
  name: z.string().optional(),
  price: z.number().optional(),
  remarks: z.string().optional(),
  is_internal: z.boolean().optional(),
  category: z.enum(PlanCategory).optional(),
})

export type UpdatePlanType = z.infer<typeof updatePlanSchema>
export type AddPlanType = z.infer<typeof addPlanSchema>
