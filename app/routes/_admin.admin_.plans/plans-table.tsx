import type { Plan } from '~/gql/graphql'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import DeleteIcon from '~/components/icons/delete-icon'
import UpdateIcon from '~/components/icons/update-icon'
import { Button } from '~/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import useBoolean from '~/hooks/use-boolean'
import useGetPlans from '~/hooks/use-get-plans'
import { parseCurrency } from '~/lib/parse-currency'
import UpdatePlanDialog from './update-plan-dialog'
import usePlansMutations from './use-plans-mutations'

export default function PlansTable() {
  const { data, isLoading, isError } = useGetPlans()
  const { isOpen: updateOpen, toggle: toggleUpdate } = useBoolean()
  const { isOpen: deleteOpen, toggle: toggleDelete } = useBoolean()

  const [selectedId, setSelectedId] = useState('')
  const [selectedPlan, setSelectedPlan] = useState<Plan>()

  const { deletePlan } = usePlansMutations()

  const handleDelete = async () => {
    if (!selectedId)
      return
    await deletePlan.mutateAsync(selectedId, {
      onSuccess: () => {
        toggleDelete(false)
      },
    })
  }

  const handleUpdate = (plan: Plan) => {
    setSelectedPlan(plan)
    toggleUpdate(true)
  }

  return (
    <>
      <div
        className={`
          mt-4 flex w-full grow overflow-x-auto rounded-md bg-white p-2
        `}
      >
        <Table className="w-full min-w-[1000px] table-fixed">
          <TableHeader>
            <TableRow>
              <TableHead className="w-75">Name</TableHead>
              <TableHead className="w-75">Price</TableHead>
              <TableHead className="w-75">Remarks</TableHead>
              <TableHead className="w-35">Internal</TableHead>
              <TableHead className="w-40">Category</TableHead>
              <TableHead className="w-75 text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading
              ? (
                  <TableRow>
                    <TableCell colSpan={5} className="py-8 text-center">
                      Loading...
                    </TableCell>
                  </TableRow>
                )
              : isError
                ? (
                    <TableRow>
                      <TableCell
                        colSpan={5}
                        className="py-8 text-center text-red-500"
                      >
                        Error loading data
                      </TableCell>
                    </TableRow>
                  )
                : data?.getPlans?.length === 0
                  ? (
                      <TableRow>
                        <TableCell colSpan={5} className="py-8 text-center">
                          No records found
                        </TableCell>
                      </TableRow>
                    )
                  : (
                      data?.getPlans?.map(plan => (
                        <TableRow key={plan.id}>
                          <TableCell className="truncate">
                            {plan.name || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {plan.price ? parseCurrency(plan.price) : '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {plan.remarks || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {plan.is_internal ? 'Yes' : 'No'}
                          </TableCell>
                          <TableCell className="truncate">
                            {plan.category || '-'}
                          </TableCell>
                          <TableCell className="flex justify-end gap-x-4">
                            <AppTooltip message="Update">
                              <Button
                                onClick={() => {
                                  handleUpdate(plan)
                                  toggleUpdate(true)
                                }}
                                size="icon"
                                variant="outline"
                              >
                                <UpdateIcon className="text-green-700" />
                              </Button>
                            </AppTooltip>
                            <AppTooltip message="Delete">
                              <Button
                                onClick={() => {
                                  setSelectedId(plan.id)
                                  toggleDelete(true)
                                }}
                                size="icon"
                                variant="outline"
                              >
                                <DeleteIcon className="text-destructive" />
                              </Button>
                            </AppTooltip>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
          </TableBody>
        </Table>
      </div>
      {selectedPlan && (
        <UpdatePlanDialog
          isOpen={updateOpen}
          toggle={toggleUpdate}
          plan={selectedPlan}
        />
      )}
      {selectedId && (
        <ConfirmationDialog
          isPending={deletePlan.isPending}
          handleConfirm={handleDelete}
          open={deleteOpen}
          handleOpenChange={toggleDelete}
        />
      )}
    </>
  )
}
