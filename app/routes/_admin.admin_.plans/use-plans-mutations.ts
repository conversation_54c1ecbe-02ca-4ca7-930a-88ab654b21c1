import type { AddPlanType, UpdatePlanType } from './schema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { ADD_PLAN, DELETE_PLAN, UPDATE_PLAN } from './graphql'

export default function usePlansMutations() {
  const queryClient = useQueryClient()

  const addPlan = useMutation({
    mutationFn: async (data: AddPlanType) => {
      const client = await graphqlClient()
      return client.request({
        document: ADD_PLAN,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success('Plan added successfully')
      queryClient.invalidateQueries({
        queryKey: ['plans'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  const updatePlan = useMutation({
    mutationFn: async (data: UpdatePlanType) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_PLAN,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success('Plan updated successfully')
      queryClient.invalidateQueries({
        queryKey: ['plans'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  const deletePlan = useMutation({
    mutationFn: async (id: string) => {
      const client = await graphqlClient()
      return client.request({
        document: DELETE_PLAN,
        variables: {
          id,
        },
      })
    },
    onSuccess: () => {
      toast.success('Plan deleted successfully')
      queryClient.invalidateQueries({
        queryKey: ['plans'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { addPlan, updatePlan, deletePlan }
}
