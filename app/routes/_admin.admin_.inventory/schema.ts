import z from 'zod'
import { PaymentMode } from '~/gql/graphql'

export const addSaleSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  products: z.array(z.object({
    product_id: z.string(),
    quantity: z.number().min(1, 'Quantity is required'),
  })).nonempty({ message: 'Please select at least one product' }),
  payment_mode: z.enum(PaymentMode).nonoptional(),
})

export const updateProductSchema = z.object({
  id: z.string().min(1, 'ID is required'),
  item_name: z.string().optional(),
  stock: z.number().optional(),
  price: z.number().optional(),
  discount: z.number().optional(),
})

export type AddSaleType = z.infer<typeof addSaleSchema>
export type UpdateProductType = z.infer<typeof updateProductSchema>
