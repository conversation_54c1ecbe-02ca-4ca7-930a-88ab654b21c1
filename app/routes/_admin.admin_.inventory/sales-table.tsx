import { format } from 'date-fns'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import PagePagination from '~/components/common/page-pagination'
import DeleteIcon from '~/components/icons/delete-icon'
import InfoIcon from '~/components/icons/info-icon'
import { Button } from '~/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import useBoolean from '~/hooks/use-boolean'
import { parseCurrency } from '~/lib/parse-currency'
import useDeleteSale from './use-delete-sale'
import useGetSales from './use-get-sales'

export default function SalesTable() {
  const { deleteSale } = useDeleteSale()
  const { isOpen, toggle } = useBoolean()
  const { data, isLoading, isError, page, lastPage, handlePage }
    = useGetSales()

  const [selectedId, setSelectedId] = useState('')

  const handleConfirmDelete = () => {
    if (!selectedId)
      return
    deleteSale.mutateAsync(selectedId, {
      onSuccess: () => {
        toggle(false)
      },
    })
  }

  // TODO: fix typing
  const calculateTotalQuantity = (products: any[]) => {
    return products.reduce((total, product) => total + product.quantity, 0)
  }

  // TODO: fix typing
  const calculateTotalAmount = (products: any[]) => {
    return products.reduce((total, product) => {
      const itemTotal = product.quantity * product.price_per_unit
      const discountAmount = product.discount
        ? (itemTotal * product.discount) / 100
        : 0
      return total + (itemTotal - discountAmount)
    }, 0)
  }

  // TODO: fix typing
  const getProductsDisplay = (products: any[]) => {
    return products.map(product => product.product.item_name).join('\n')
  }

  return (
    <>
      <div
        className={`
          mt-4 flex w-full grow overflow-x-auto rounded-md bg-white p-2
        `}
      >
        <Table className="w-full min-w-[1000px] table-fixed">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[150px]">Name</TableHead>
              <TableHead className="w-[200px]">Address</TableHead>
              <TableHead className="w-[140px]">Phone number</TableHead>
              <TableHead className="w-[140px]">Particulars</TableHead>
              <TableHead className="w-[140px]">Quantity</TableHead>
              <TableHead className="w-[140px]">Amount</TableHead>
              <TableHead className="w-[140px]">Payment date</TableHead>
              <TableHead className="w-[140px]">Payment mode</TableHead>
              <TableHead className="w-[50px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading
              ? (
                  <TableRow>
                    <TableCell colSpan={8} className="py-8 text-center">
                      Loading...
                    </TableCell>
                  </TableRow>
                )
              : isError
                ? (
                    <TableRow>
                      <TableCell
                        colSpan={8}
                        className="py-8 text-center text-red-500"
                      >
                        Error loading data
                      </TableCell>
                    </TableRow>
                  )
                : data?.getSales?.data?.length === 0
                  ? (
                      <TableRow>
                        <TableCell colSpan={8} className="py-8 text-center">
                          No records found
                        </TableCell>
                      </TableRow>
                    )
                  : (
                      data?.getSales?.data?.map(sale => (
                        <TableRow key={sale.id}>
                          <TableCell className="truncate">
                            {sale.customer.name || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {sale.customer.address || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {sale.customer.phone_number || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {sale.productSales.length >= 1 && (
                              <AppTooltip
                                className="whitespace-pre-line"
                                message={getProductsDisplay(sale.productSales)}
                              >
                                <Button variant="ghost" size="icon">
                                  <InfoIcon />
                                </Button>
                              </AppTooltip>
                            )}
                          </TableCell>
                          <TableCell className="truncate">
                            {calculateTotalQuantity(sale.productSales)}
                          </TableCell>
                          <TableCell className="truncate">
                            {parseCurrency(calculateTotalAmount(sale.productSales))}
                          </TableCell>
                          <TableCell className="truncate">
                            {sale.created_at
                              ? format(new Date(sale.created_at), 'do MMM yy')
                              : '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {sale.payment_mode}
                          </TableCell>
                          <TableCell>
                            <div className="flex justify-end">
                              <AppTooltip message="Delete">
                                <Button
                                  onClick={() => {
                                    setSelectedId(sale.id)
                                    toggle(true)
                                  }}
                                  variant="destructive"
                                  size="icon"
                                >
                                  <DeleteIcon />
                                </Button>
                              </AppTooltip>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
          </TableBody>
        </Table>
      </div>
      {lastPage > 1 && (
        <div className="flex justify-center">
          <PagePagination
            lastPage={lastPage}
            currentPage={page}
            handlePagePagination={handlePage}
          />
        </div>
      )}
      {selectedId && (
        <ConfirmationDialog
          isPending={deleteSale.isPending}
          handleConfirm={handleConfirmDelete}
          open={isOpen}
          handleOpenChange={toggle}
        />
      )}
    </>
  )
}
