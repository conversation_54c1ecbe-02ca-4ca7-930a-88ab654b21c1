import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { DELETE_SALE } from './graphql'

export default function useDeleteSale() {
  const queryClient = useQueryClient()

  const deleteSale = useMutation({
    mutationFn: async (id: string) => {
      const client = await graphqlClient()
      return client.request({
        document: DELETE_SALE,
        variables: {
          id,
        },
      })
    },
    onSuccess: () => {
      toast.success('Sale deleted successfully')
      queryClient.invalidateQueries({
        queryKey: ['get-sales'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { deleteSale }
}
