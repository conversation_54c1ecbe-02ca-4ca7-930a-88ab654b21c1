import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import useGetSales from './use-get-sales'

export default function SalesQueries() {
  const { name, startDate, endDate, handleSearch } = useGetSales()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSearch({ [e.target.name]: e.target.value })
  }

  return (
    <div className="mt-4 grid grid-cols-4 gap-4">
      <Label className="col-span-1 flex flex-col items-start gap-y-2">
        <div>Product name</div>
        <Input placeholder="Search by product name" value={name} name="name" onChange={handleInputChange} />
      </Label>
      <Label className="col-span-1 flex flex-col items-start gap-y-2">
        <div>Start Date</div>
        <Input
          name="startDate"
          placeholder="Search by start date"
          className="block"
          type="date"
          value={startDate}
          onChange={handleInputChange}
        />
      </Label>
      <Label className="col-span-1 flex flex-col items-start gap-y-2">
        <div>End Date</div>
        <Input name="endDate" placeholder="Search by end date" className="block" type="date" value={endDate} onChange={handleInputChange} />
      </Label>
    </div>
  )
}
