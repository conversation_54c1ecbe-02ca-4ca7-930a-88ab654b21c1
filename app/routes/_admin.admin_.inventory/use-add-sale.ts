import type { AddSaleType } from './schema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { ADD_SALE } from './graphql'

export default function useAddSale() {
  const queryClient = useQueryClient()

  const addSale = useMutation({
    mutationFn: async (data: AddSaleType) => {
      const client = await graphqlClient()
      return client.request({
        document: ADD_SALE,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success('Sale added successfully')
      queryClient.invalidateQueries({
        queryKey: ['get-sales'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { addSale }
}
