import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { useDebounce } from 'use-debounce'
import { graphqlClient } from '~/lib/graphql-client'
import { SEARCH_CUSTOMER_BY_NAME } from './graphql'

export default function useSearchCustomers() {
  const [name, setName] = useState('')
  const [debouncedName] = useDebounce(name, 500)

  const handleNameChange = (e: string) => {
    setName(e)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['search-customers', debouncedName],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: SEARCH_CUSTOMER_BY_NAME,
        variables: {
          name: debouncedName,
        },
      })
    },
    enabled: !!name,
  })

  return { data, isLoading, isError, name, handleNameChange }
}
