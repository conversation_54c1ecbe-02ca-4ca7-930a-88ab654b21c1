import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { parseAsInteger, parseAsString, useQueryState } from 'nuqs'
import { useDebounce } from 'use-debounce'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_SALES } from './graphql'

export default function useGetSales() {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))
  const [name, setName] = useQueryState('name', parseAsString.withDefault(''))
  const [startDate, setStartDate] = useQueryState('start_date', parseAsString.withDefault(''))
  const [endDate, setEndDate] = useQueryState('end_date', parseAsString.withDefault(''))

  const [debouncedName] = useDebounce(name, 500)

  const handlePage = (page: number) => {
    setPage(page)
  }

  const handleSearch = (params: {
    name?: string
    zoneId?: string
    subscriptionStatus?: string
    type?: string
    paymentMode?: string
    month?: string
    startDate?: string
    endDate?: string
    phoneNumber?: string
    customerType?: string
  }) => {
    if (params.name !== undefined)
      setName(params.name)
    if (params.startDate !== undefined)
      setStartDate(params.startDate)
    if (params.endDate !== undefined)
      setEndDate(params.endDate)
    setPage(1) // Reset to first page when searching
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-sales', page, debouncedName, startDate, endDate],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_SALES,
        variables: {
          first: 15,
          page,
          product_name: debouncedName || undefined,
          start_date: startDate ? format(new Date(startDate), 'yyyy-MM-dd hh:mm:ss') : undefined,
          end_date: endDate ? format(new Date(endDate), 'yyyy-MM-dd hh:mm:ss') : undefined,
        },
      })
    },
  })

  const lastPage = data?.getSales?.paginator_info?.last_page || 1

  return { data, isLoading, isError, handlePage, handleSearch, page, name, startDate, endDate, lastPage }
}
