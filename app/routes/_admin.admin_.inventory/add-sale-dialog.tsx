import type { AddSaleType } from './schema'
import type { PaymentMode, Product } from '~/gql/graphql'
import { CheckIcon, ChevronsUpDownIcon } from 'lucide-react'
import FormMessage from '~/components/common/form-message'
import { Button } from '~/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '~/components/ui/command'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import { parseCurrency } from '~/lib/parse-currency'
import { cn } from '~/lib/utils'
import { addSaleSchema } from './schema'
import useAddSale from './use-add-sale'
import useSearchCustomers from './use-search-customers'

interface Props {
  products: Product[]
}

export default function AddSaleDialog({ products }: Props) {
  const { isOpen, toggle } = useBoolean()
  const { isOpen: popoverOpen, toggle: togglePopover } = useBoolean()

  const { addSale } = useAddSale()

  const { data, isLoading, isError, name, handleNameChange } = useSearchCustomers()

  const form = useAppForm({
    defaultValues: {
      customer_id: '',
      products: [],
      payment_mode: '' as unknown as PaymentMode,
    } as AddSaleType,
    validators: {
      onSubmit: addSaleSchema,
    },
    onSubmit: async ({ value }) => {
      await addSale.mutateAsync(value, {
        onSuccess: () => {
          form.reset()
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        toggle(open)
        form.reset()
      }}
    >
      <DialogTrigger asChild>
        <div className="flex justify-end">
          <Button variant="outline">
            Add Sale
          </Button>
        </div>
      </DialogTrigger>
      <DialogContent className="w-full min-w-4xl">
        <DialogHeader>
          <DialogTitle>
            Add new sale
          </DialogTitle>
          <DialogDescription>
            Enter sale details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-y-4"
        >
          <form.AppField
            name="customer_id"
            children={field => (
              <Popover modal={true} open={popoverOpen} onOpenChange={togglePopover}>
                <PopoverTrigger asChild>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Select Customer</div>
                    <Button
                      type="button"
                      variant="outline"
                      role="combobox"
                      aria-expanded={popoverOpen}
                      className="w-full justify-between"
                    >
                      {field.state.value
                        ? data?.getCustomers?.data?.find(cat => cat.id === field.state.value)?.name
                        : 'Search customer...'}
                      <ChevronsUpDownIcon className={`
                        ml-2 h-4 w-4 shrink-0 opacity-50
                      `}
                      />
                    </Button>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </PopoverTrigger>
                <PopoverContent className="w-full min-w-3xl p-0">
                  <Command className="w-full">
                    <CommandInput
                      isLoading={isLoading}
                      value={name}
                      onValueChange={handleNameChange}
                      placeholder="Search categories..."
                    />
                    <CommandList className="w-full">
                      {!isLoading && <CommandEmpty>No customer found.</CommandEmpty>}
                      {isError && (
                        <div className="py-6 text-center text-sm">
                          Error fetching customer list.
                        </div>
                      )}
                      <CommandGroup>
                        {data?.getCustomers?.data?.map(customer => (
                          <CommandItem
                            key={customer.id}
                            value={customer.name}
                            onSelect={() => {
                              field.handleChange(field.state.value === customer.id ? '' : customer.id)
                              togglePopover(false)
                            }}
                          >
                            <CheckIcon
                              className={cn(
                                'mr-2 h-4 w-4',
                                field.state.value === customer.id
                                  ? `opacity-100`
                                  : `opacity-0`,
                              )}
                            />
                            {customer.name}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            )}
          />
          <form.AppField
            name="products"
            children={field => (
              <div className="flex flex-col gap-y-2">
                <Label>Products</Label>
                <div className="flex flex-col gap-y-2 border bg-gray-100 p-2">
                  {products.map((product) => {
                    const selectedProduct = field.state.value.find(p => p.product_id === product.id)
                    const currentQuantity = selectedProduct?.quantity || 0

                    return (
                      <div
                        key={product.id}
                        className={`
                          col-span-2 flex items-center justify-between gap-2
                        `}
                      >
                        <span className="flex-1">{product.item_name}</span>
                        <div className="flex items-center gap-1">
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              const newQuantity = Math.max(0, currentQuantity - 1)
                              if (newQuantity === 0) {
                                field.handleChange(field.state.value.filter(p => p.product_id !== product.id))
                              }
                              else {
                                field.handleChange(
                                  field.state.value.map(p =>
                                    p.product_id === product.id ? { ...p, quantity: newQuantity } : p,
                                  ),
                                )
                              }
                            }}
                            disabled={currentQuantity <= 0}
                          >
                            -
                          </Button>
                          <Input
                            min="0"
                            value={currentQuantity}
                            onChange={(e) => {
                              const quantity = Math.max(0, Number.parseInt(e.target.value) || 0)
                              if (quantity === 0) {
                                field.handleChange(field.state.value.filter(p => p.product_id !== product.id))
                              }
                              else {
                                if (selectedProduct) {
                                  field.handleChange(
                                    field.state.value.map(p =>
                                      p.product_id === product.id ? { ...p, quantity } : p,
                                    ),
                                  )
                                }
                                else {
                                  field.handleChange([...field.state.value, { product_id: product.id, quantity }])
                                }
                              }
                            }}
                            className={`
                              w-16 border border-black bg-white text-center
                            `}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              const newQuantity = currentQuantity + 1
                              if (selectedProduct) {
                                field.handleChange(
                                  field.state.value.map(p =>
                                    p.product_id === product.id ? { ...p, quantity: newQuantity } : p,
                                  ),
                                )
                              }
                              else {
                                field.handleChange([...field.state.value, { product_id: product.id, quantity: 1 }])
                              }
                            }}
                          >
                            +
                          </Button>
                        </div>
                      </div>
                    )
                  })}
                  <div className="flex justify-between border-t-2 py-2">
                    <div>Total</div>
                    <div>
                      {parseCurrency(
                        field.state.value.reduce((total, selectedProduct) => {
                          const product = products.find(p => p.id === selectedProduct.product_id)
                          if (!product)
                            return total

                          const itemTotal = selectedProduct.quantity * product.price
                          const discountAmount = product.discount
                            ? (itemTotal * product.discount) / 100
                            : 0

                          return total + (itemTotal - discountAmount)
                        }, 0),
                      )}
                    </div>
                  </div>
                </div>
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </div>
            )}
          />
          <form.AppField
            name="payment_mode"
            children={field => (
              <>
                <Label className="flex flex-col items-start gap-y-2">
                  <div>Payment Mode</div>
                  <Select
                    value={field.state.value}
                    onValueChange={(e) => {
                      field.handleChange(e as PaymentMode)
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CASH">Cash</SelectItem>
                      <SelectItem value="CHEQUE">Cheque</SelectItem>
                      <SelectItem value="UPI">UPI</SelectItem>
                      <SelectItem value="CARD">Card</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                </Label>
              </>
            )}
          />
          <DialogFooter className="col-span-2 mt-2">
            <form.AppForm>
              <form.SubmitButton label="Add Sale" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
