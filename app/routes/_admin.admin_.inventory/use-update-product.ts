import type { UpdateProductType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { useRevalidator } from 'react-router'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UPDATE_PRODUCT } from './graphql'

export default function useUpdateProduct() {
  const revalidator = useRevalidator()

  const updateProduct = useMutation({
    mutationFn: async (data: UpdateProductType) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_PRODUCT,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success('Product updated successfully')
      revalidator.revalidate()
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { updateProduct }
}
