import { graphql } from '~/gql'

export const UPDATE_PRODUCT = graphql(`
  mutation UpdateProduct(
    $id: ID!
    $item_name: String
    $stock: Int
    $price: Float
    $discount: Float
  ) {
    updateProduct(id: $id, item_name: $item_name, stock: $stock, price: $price, discount: $discount)
  }
`)

export const GET_PRODUCTS = graphql(`
  query GetProducts {
    getProducts {
      id
      item_name
      stock
      price
      discount
    }
  }
`)

export const SEARCH_CUSTOMER_BY_NAME = graphql(`
  query SearchCustomerByName($name: String!) {
    getCustomers(first: 20, name: $name) {
      data {
        id
        name
      }
    }
  }
`)

export const ADD_SALE = graphql(`
  mutation AddSale(
    $customer_id: ID!
    $payment_mode: PaymentMode!
    $products: [ProductInput!]!
  ) {
    addSale(
      customer_id: $customer_id
      payment_mode: $payment_mode
      products: $products
    )
  }
`)

export const DELETE_SALE = graphql(`
  mutation DeleteSale(
    $id: ID!
  ) {
    deleteSale(id: $id) {
      id
    }
  }
`)

export const GET_SALES = graphql(`
  query GetSales(
    $first: Int!
    $page: Int
    $product_name: String
    $start_date: DateTime
    $end_date: DateTime
  ) {
    getSales(
      first: $first
      page: $page
      product_name: $product_name
      start_date: $start_date
      end_date: $end_date
    ) {
      data {
        id
        payment_mode
        customer {
          id
          name
          address
          phone_number
        }
        productSales {
          id
          quantity
          price_per_unit
          discount
          product {
            id
            item_name
          }
        }
        created_at
      }
      paginator_info {
        last_page
      }
    }
  }
`)
