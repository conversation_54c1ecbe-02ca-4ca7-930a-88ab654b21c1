import { useLoaderData } from 'react-router'
import PageHeader from '~/components/common/page-header'
import { graphqlClient } from '~/lib/graphql-client'
import AddSaleDialog from './add-sale-dialog'
import { GET_PRODUCTS } from './graphql'
import ProductsRow from './products-row'
import SalesQueries from './sales-queries'
import SalesTable from './sales-table'

export async function loader() {
  const client = await graphqlClient()

  const data = await client.request({
    document: GET_PRODUCTS,
  })

  const products = data.getProducts || []

  return {
    products,
  }
}

export default function Inventory() {
  const { products } = useLoaderData<typeof loader>()

  return (
    <div className="flex grow flex-col gap-4">
      <PageHeader title="Inventory" />
      <ProductsRow products={products} />
      <SalesQueries />
      <SalesTable />
      <div className="flex justify-end">
        <AddSaleDialog products={products} />
      </div>
    </div>
  )
}
