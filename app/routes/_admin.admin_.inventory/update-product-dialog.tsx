import type { UpdateProductType } from './schema'
import type { Product } from '~/gql/graphql'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { useAppForm } from '~/hooks/form'
import { updateProductSchema } from './schema'
import useUpdateProduct from './use-update-product'

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  product: Product
}

export default function UpdateProductDialog({ product, isOpen, toggle }: Props) {
  const { updateProduct } = useUpdateProduct()

  const form = useAppForm({
    defaultValues: {
      id: product.id,
      item_name: product.item_name,
      stock: product.stock,
      price: product.price,
      discount: product.discount,
    } as UpdateProductType,
    validators: {
      onSubmit: updateProductSchema,
    },
    onSubmit: async ({ value }) => {
      await updateProduct.mutateAsync(value, {
        onSuccess: () => {
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Product</DialogTitle>
          <DialogDescription>Enter new values</DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="gap-y flex flex-col"
        >
          <form.AppField
            name="item_name"
            children={field => <field.InputField label="Item Name" />}
          />
          <form.AppField
            name="stock"
            children={field => <field.NumberField label="Stock" />}
          />
          <form.AppField
            name="price"
            children={field => <field.NumberField label="Price" />}
          />
          <form.AppField
            name="discount"
            children={field => <field.NumberField label="Discount" />}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Update Product" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
