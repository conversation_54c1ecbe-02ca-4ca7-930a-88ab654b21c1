import type { Product } from '~/gql/graphql'
import { useState } from 'react'
import useBoolean from '~/hooks/use-boolean'
import { parseCurrency } from '~/lib/parse-currency'
import UpdateProductDialog from './update-product-dialog'

interface Props {
  products: Product[]
}

function formatItemName(itemName: string) {
  const parts = itemName.split('(')
  if (parts.length > 1) {
    return (
      <>
        {parts[0].trim()}
        <br />
        (
        {parts.slice(1).join('(')}
      </>
    )
  }
  return itemName
}

export default function ProductsRow({ products }: Props) {
  const { isOpen, toggle } = useBoolean()
  const [selectedProduct, setSelectedProduct] = useState<Product>()

  const handleSelectedProduct = (product: Product) => {
    setSelectedProduct(product)
    toggle(true)
  }

  return (
    <>
      <div className="grid grid-cols-8 gap-4">
        {products.map(product => (
          <div
            key={product.id}
            onClick={() => handleSelectedProduct(product)}
            role="button"
            className={`
              col-span-1 flex flex-col items-center gap-y-2 rounded-md bg-white
              p-4 text-sm
            `}
          >
            <p className="text-center font-bold">{formatItemName(product.item_name)}</p>
            <div className="w-full border border-gray-200" />
            <p>
              Stock:
              {' '}
              {product.stock}
            </p>
            <p>
              Price:
              {' '}
              {parseCurrency(product.price)}
            </p>
            <p>
              Discount:
              {' '}
              {product.discount}
              %
            </p>
          </div>
        ))}
      </div>
      {selectedProduct && (
        <UpdateProductDialog product={selectedProduct} isOpen={isOpen} toggle={toggle} />
      )}
    </>
  )
}
