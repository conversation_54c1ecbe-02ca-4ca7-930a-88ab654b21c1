import type { RegisterCustomerType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { REGISTER_CUSTOMER } from '../_admin.admin_.customers/graphql'

export default function useRegisterCustomer() {
  const registerCustomer = useMutation({
    mutationFn: async (data: RegisterCustomerType) => {
      const client = await graphqlClient()
      return client.request({
        document: REGISTER_CUSTOMER,
        variables: {
          ...data,
        },
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { registerCustomer }
}
