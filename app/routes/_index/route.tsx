import { Alarm<PERSON>lock, Calendar, CalendarX, Clock3, House, MapPin, ScrollText, ShieldCheck, Sparkles, <PERSON>prout, Trash2, <PERSON>, Zap } from 'lucide-react'
import { Link, useLoaderData } from 'react-router'
import { GET_ZONES_AND_PLANS } from '~/graphql/queries/get-zones-and-plans'
import { graphqlClient } from '~/lib/graphql-client'
import { parseCurrency } from '~/lib/parse-currency'
import { cn } from '~/lib/utils'
import SubscribeDialog from './subscribe-dialog'

function SmallCard({ icon, description, className }: { className: string, icon: React.ReactNode, description: string }) {
  return (
    <div className={`
      col-span-2 flex flex-col items-center gap-4 rounded-lg bg-white p-8
      shadow-xl inset-shadow-sm
      md:col-span-1 md:flex-row
    `}
    >
      <div className={cn(`
        flex size-12 shrink-0 items-center justify-center rounded-full
      `, className)}
      >
        {icon}
      </div>

      <div className={`
        text-center
        md:text-start md:text-xl
      `}
      >
        {description}
      </div>
    </div>
  )
}

function ColumnCard({ icon, className, iconClassName, title, description1, description2 }: { className?: string, iconClassName: string, icon: React.ReactNode, title: string, description1: string, description2?: string }) {
  return (
    <div className={cn(`
      col-span-2 flex flex-col items-center gap-y-4
      md:col-span-1 md:p-8
    `, className)}
    >
      <div className={cn(`
        flex size-24 items-center justify-center rounded-full text-2xl
      `, iconClassName)}
      >
        {icon}
      </div>
      <div className="flex flex-col items-center">
        <div className="font-weight-[500] mb-4 text-2xl">{title}</div>
        <div className="text-center text-lg">{description1}</div>
        <div className="text-center text-lg">{description2}</div>
      </div>
    </div>
  )
}

export function meta() {
  return [
    // Basic Meta Tags
    { title: 'Aizawl Garbo - Your Trash, Our Priority' },
    {
      name: 'description',
      content: 'Reliable garbage collection and waste management services in Aizawl, Mizoram. Your Trash, Our Priority.',
    },
    {
      name: 'keywords',
      content: 'garbage collection Aizawl, waste management Mizoram, trash pickup Aizawl, waste disposal services, recycling Mizoram, sanitation services, municipal waste, commercial waste collection',
    },
    {
      name: 'author',
      content: 'Aizawl Garbo',
    },
    {
      name: 'viewport',
      content: 'width=device-width, initial-scale=1.0',
    },
    {
      name: 'robots',
      content: 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1',
    },
    {
      name: 'googlebot',
      content: 'index, follow',
    },

    // Geographic and Local SEO
    {
      name: 'geo.region',
      content: 'IN-MZ',
    },
    {
      name: 'geo.placename',
      content: 'Aizawl, Mizoram, India',
    },
    {
      name: 'geo.position',
      content: '23.7271;92.7176',
    },
    {
      name: 'ICBM',
      content: '23.7271, 92.7176',
    },

    // Open Graph Tags
    {
      property: 'og:title',
      content: 'Aizawl Garbo - Your Trash, Our Priority',
    },
    {
      property: 'og:description',
      content: 'Reliable garbage collection and waste management services in Aizawl, Mizoram. Your Trash, Our Priority.',
    },
    {
      property: 'og:type',
      content: 'website',
    },
    {
      property: 'og:url',
      content: 'https://aizawlgarbo.com',
    },
    {
      property: 'og:site_name',
      content: 'Aizawl Garbo',
    },
    {
      property: 'og:image',
      content: '/logo.jpg',
    },
    {
      property: 'og:image:alt',
      content: 'Aizawl Garbo - Waste Management Services Logo',
    },
    {
      property: 'og:image:width',
      content: '1200',
    },
    {
      property: 'og:image:height',
      content: '630',
    },
    {
      property: 'og:locale',
      content: 'en_IN',
    },

    // Twitter Card Tags
    {
      name: 'twitter:card',
      content: 'summary_large_image',
    },
    {
      name: 'twitter:title',
      content: 'Aizawl Garbo - Your Trash, Our Priority',
    },
    {
      name: 'twitter:description',
      content: 'Reliable garbage collection and waste management services in Aizawl, Mizoram. Your Trash, Our Priority.',
    },
    {
      name: 'twitter:image',
      content: '/logo.jpg',
    },
    {
      name: 'twitter:image:alt',
      content: 'Aizawl Garbo Logo',
    },
    {
      name: 'twitter:url',
      content: 'https://aizawlgarbo.com',
    },

    // Business/Local Business Schema prep
    {
      name: 'business:contact_data:locality',
      content: 'Aizawl',
    },
    {
      name: 'business:contact_data:region',
      content: 'Mizoram',
    },
    {
      name: 'business:contact_data:country_name',
      content: 'India',
    },

    // Additional SEO
    {
      name: 'theme-color',
      content: '#2E7D32', // Suggested green theme for waste management
    },
    {
      name: 'apple-mobile-web-app-title',
      content: 'Aizawl Garbo',
    },
    {
      name: 'application-name',
      content: 'Aizawl Garbo',
    },

    // Canonical URL
    {
      rel: 'canonical',
      href: 'https://aizawlgarbo.com',
    },
  ]
}

export async function loader() {
  const client = await graphqlClient()

  const data = await client.request({
    document: GET_ZONES_AND_PLANS,
  })

  const plans = data.getPlans || []

  return {
    plans,
  }
}

export default function Index() {
  const { plans } = useLoaderData<typeof loader>()

  return (
    <div className="flex size-full min-h-screen flex-col">
      <div className={`
        grid grid-cols-12 bg-[#f0f8d3] p-8
        md:p-24
      `}
      >
        <div className={`
          order-2 col-span-12 flex flex-col gap-y-4
          md:order-1 md:col-span-5
        `}
        >
          <h1 className={`
            text-3xl font-bold text-green-600
            md:text-6xl
          `}
          >
            AizawlGarbo
          </h1>
          <div className={`
            text-2xl font-bold
            md:text-6xl
          `}
          >
            Your Trash, Our Priority
          </div>
          <div className="md:text-lg">
            <div>No More Waiting for the Garbage Truck.</div>
            <div>Doorstep garbage collection.</div>
            <div>On your schedule.</div>
          </div>

          <div className="flex justify-start">
            <SubscribeDialog plans={plans} />

          </div>
        </div>
        <div className={`
          order-1 col-span-12 flex size-full justify-end
          md:order-2 md:col-span-7
        `}
        >
          <img
            className={`
              w-full
              md:w-2/3
            `}
            src="/aizawl_garbo_landing.png"
          />

        </div>

      </div>

      <div className={`
        p-8
        md:p-24
      `}
      >
        <div className={`
          mb-12 text-center text-2xl font-bold
          md:mb-24 md:text-4xl
        `}
        >
          "Tired of waiting for the Garbage Truck"
        </div>
        <div className={`
          mx-auto mt-8 grid grid-cols-2 gap-16
          md:w-2/3
        `}
        >
          <SmallCard className="bg-destructive/30 text-destructive" icon={<Clock3 />} description="You never know when it will show up" />
          <SmallCard className="bg-destructive/30 text-destructive" icon={<CalendarX />} description="You can't pick your own collection days" />
          <SmallCard className="bg-destructive/30 text-destructive" icon={<Trash2 />} description="Biodegradable waste starts to smell and gets gross if left too" />
          <SmallCard className="bg-destructive/30 text-destructive" icon={<AlarmClock />} description="You waste time and energy just to throw out trash" />
        </div>
      </div>

      <section className={`
        bg-[#f3fff3] p-8
        md:p-24
      `}
      >
        <div className={`
          mb-12 text-center text-2xl font-bold
          md:mb-24 md:text-4xl
        `}
        >
          "Trash-free, stress-free"
        </div>
        <div className={`
          mx-auto mt-8 grid grid-cols-2 gap-16
          md:w-2/3
        `}
        >
          <SmallCard className="bg-[#d2ebd3] text-green-700" icon={<Calendar />} description="Choose your preferred pickup days." />
          <SmallCard className="bg-[#d2ebd3] text-green-700" icon={<MapPin />} description="We come right to your doorstep, no waiting around." />
          <SmallCard className="bg-[#d2ebd3] text-green-700" icon={<Clock3 />} description="Regular, reliable collections you can count on." />
          <SmallCard className="bg-[#d2ebd3] text-green-700" icon={<Sparkles />} description="Save time, avoid the mess and keep your home fresh." />
        </div>
      </section>

      <section className={`
        bg-[#f3fff3] p-8
        md:p-24
      `}
      >
        <div className={`
          mb-12 text-center text-2xl font-bold
          md:mb-24 md:text-4xl
        `}
        >
          How it works
        </div>
        <div className={`
          mx-auto mt-8 grid grid-cols-2 gap-8
          md:w-2/3 md:grid-cols-3
        `}
        >
          <ColumnCard
            iconClassName="bg-green-600 text-white"
            icon={<ScrollText className="size-10" />}
            title="Choose a Plan"
            description1="Once, Twice or Thrice a week."
            description2="Pick what works for your budget."
          />
          <ColumnCard
            iconClassName="bg-green-600 text-white"
            icon={<Calendar className="size-10" />}
            title="Set your days"
            description1="Pick the days that work best for you."
            description2="You're in control."
          />
          <ColumnCard
            iconClassName="bg-green-600 text-white"
            icon={<House className="size-10" />}
            title="We Pick it Up"
            description1="From your doorstep without the wait"
            description2="Reliable and stress free."
          />
        </div>
      </section>

      <section className={`
        p-8
        md:p-24
      `}
      >
        <div className={`
          text-center text-2xl font-bold
          md:text-4xl
        `}
        >
          Choose Your Plan
        </div>
        <div className={`
          mt-4 text-center text-lg text-gray-500
          md:text-2xl
        `}
        >
          Affordable, flexible subscriptions for every household
        </div>
        <div className="overflow-x-auto">
          <table className={`
            mx-auto mt-12 w-full min-w-[720px]
            md:max-w-1/2
          `}
          >
            <thead>
              <tr>
                <th className="w-2/5 p-2 text-center font-normal">
                  Subscription type
                </th>
                <th className="w-1/5 p-2 text-center font-normal">
                  Price
                </th>
                <th className="w-1/5 p-2 text-center font-normal">
                  No of collection
                </th>
                <th className="w-1/5 p-2 text-center font-normal">
                  Collection schedule
                </th>
              </tr>
            </thead>
            <div></div>
            <tbody className="rounded-lg border border-black">
              <tr>
                <td
                  rowSpan={3}
                  className={`
                    border-r border-b border-black bg-slate-100 text-center
                    align-middle text-2xl font-bold
                  `}
                >
                  Domestic
                </td>
                <td className="border-r border-b border-black p-2 text-center">
                  {parseCurrency(350, 0)}
                  {' '}
                  / month
                </td>
                <td className="border-r border-b border-black p-2 text-center">
                  1 x per week
                </td>
                <td className="border-b border-black p-2 text-center">
                  Tue or Fri
                </td>
              </tr>
              <tr>
                <td className="border-r border-b border-black p-2 text-center">
                  {parseCurrency(450, 0)}
                  {' '}
                  / month
                </td>
                <td className="border-r border-b border-black p-2 text-center">
                  2 x per week
                </td>
                <td className="border-b border-black p-2 text-center">
                  Tue and Fri
                </td>
              </tr>
              <tr>
                <td className="border-r border-b border-black p-2 text-center">
                  {parseCurrency(550, 0)}
                  {' '}
                  / month
                </td>
                <td className="border-r border-b border-black p-2 text-center">
                  3 x per week
                </td>
                <td className="border-b border-black p-2 text-center">
                  Tue, Thu and Sat
                </td>
              </tr>
              <tr>
                <td
                  rowSpan={3}
                  className={`
                    border-r border-black bg-slate-100 text-center align-middle
                    text-2xl font-bold
                  `}
                >
                  Commercial
                </td>
                <td className="border-r border-b border-black p-2 text-center">
                  {parseCurrency(450, 0)}
                  {' '}
                  / month
                </td>
                <td className="border-r border-b border-black p-2 text-center">
                  1 x per week
                </td>
                <td className="border-b border-black p-2 text-center">
                  Tue or Fri
                </td>
              </tr>
              <tr>
                <td className="border-r border-b border-black p-2 text-center">
                  {parseCurrency(800, 0)}
                  {' '}
                  / month
                </td>
                <td className="border-r border-b border-black p-2 text-center">
                  2 x per week
                </td>
                <td className="border-b border-black p-2 text-center">
                  Tue and Fri
                </td>
              </tr>
              <tr>
                <td className="border-r border-black p-2 text-center">
                  {parseCurrency(1200, 0)}
                  {' '}
                  / month
                </td>
                <td className="border-r border-black p-2 text-center">
                  3 x per week
                </td>
                <td className="p-2 text-center">
                  Tue, Thu and Sat
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <section className={`
        bg-gray-100 p-8
        md:p-24
      `}
      >
        <div className="mb-24 text-center text-4xl font-bold">
          Why Choose AizawlGarbo?
        </div>
        <div className={`
          mx-auto mt-8 grid w-full grid-cols-4 gap-8
          md:w-[75%]
        `}
        >
          <div className={`
            col-span-4 flex flex-col items-center gap-y-4 rounded-lg border
            bg-white p-4
            md:col-span-1
          `}
          >
            <div className={`
              flex size-16 items-center justify-center rounded-full bg-green-600
              text-2xl text-white
            `}
            >
              <ShieldCheck className="size-8" />
            </div>
            <div className="flex flex-col items-center">
              <div className={`
                font-weight-[500] mb-4 text-center text-lg font-bold
              `}
              >
                Reliable Doorstep Pickup
              </div>
              <div className="text-center text-sm text-gray-400">Consistent collection right from your doorstep with professional staff you can trust</div>
            </div>
          </div>
          <div className={`
            col-span-4 flex flex-col items-center gap-y-4 rounded-lg border
            bg-white p-4
            md:col-span-1
          `}
          >
            <div className={`
              flex size-16 items-center justify-center rounded-full bg-green-600
              text-2xl text-white
            `}
            >
              <Zap className="size-8" />
            </div>
            <div className="flex flex-col items-center">
              <div className={`
                font-weight-[500] mb-4 text-center text-lg font-bold
              `}
              >
                Save time and energy
              </div>
              <div className="text-center text-sm text-gray-400">Leave your trash at the door and we take care of the rest</div>
            </div>
          </div>
          <div className={`
            col-span-4 flex flex-col items-center gap-y-4 rounded-lg border
            bg-white p-4
            md:col-span-1
          `}
          >
            <div className={`
              flex size-16 items-center justify-center rounded-full bg-green-600
              text-2xl text-white
            `}
            >
              <Sprout className="size-8" />
            </div>
            <div className="flex flex-col items-center">
              <div className={`
                font-weight-[500] mb-4 text-center text-lg font-bold
              `}
              >
                Clean and Hygienic
              </div>
              <div className="text-center text-sm text-gray-400">Regular pickups mean fresh homes and no more lingering odours from old waste</div>
            </div>
          </div>
          <div className={`
            col-span-4 flex flex-col items-center gap-y-4 rounded-lg border
            bg-white p-4
            md:col-span-1
          `}

          >
            <div className={`
              flex size-16 items-center justify-center rounded-full bg-green-600
              text-2xl text-white
            `}
            >
              <Users className="size-10" />
            </div>
            <div className="flex flex-col items-center">
              <div className={`
                font-weight-[500] mb-4 text-center text-lg font-bold
              `}
              >
                Local Service
              </div>
              <div className="text-center text-sm text-gray-400">Affordable subscriptions designed specifically for Aizawl families and neighborhoods</div>
            </div>
          </div>
        </div>
      </section>

      <section className={`
        p-8
        md:p-24
      `}
      >
        <div className={`
          mb-4 text-center text-2xl font-bold
          md:text-4xl
        `}
        >
          "Your cleaner and healthier home, Just a Tap Away."
        </div>
        <div className={`
          text-center text-xl text-gray-400
          md:text-3xl
        `}
        >
          Join families across Aizawl who've taken control of their trash collection
        </div>
        <SubscribeDialog plans={plans} />
      </section>

      <footer className={`
        bg-[#323232] p-8 text-white
        md:p-24
      `}
      >
        <div className={`
          flex flex-col justify-between gap-y-4
          md:flex-row
        `}
        >
          <div className="flex flex-col gap-y-4">
            <div className="text-4xl font-bold">AizawlGarbo</div>
            <div>
              <p>
                Professional door-to-door garbage collection service in Aizawl.
              </p>
              <p>
                Keeping our city clean, one household at a time.
              </p>
            </div>

            <div className="text-xl font-bold">
              Contact Us
            </div>
            <div>
              <p>
                9515733784
              </p>
              <p>
                <EMAIL>
              </p>
            </div>

          </div>

          <div className="flex flex-col gap-y-4">
            <div className="text-xl font-bold">
              Quick Links
            </div>

            <div className="flex flex-col">
              <Link to="/">Plans</Link>
              <Link to="/">FAQ</Link>
              <Link to="/">Terms & Conditions</Link>
              <Link to="/">Privacy Policy</Link>
              <Link to="/">Shipping Policy</Link>
              <Link to="/login">Login</Link>
            </div>
          </div>
        </div>

        <div className="my-16 w-full border border-white" />

        <div className="flex flex-col items-center justify-center">
          <div>
            <img src="/arsi_logo.svg" className="size-24" />
          </div>
          <div className="mt-4">
            Designed and developed by
          </div>
          <div>
            Arsi Consultancy
          </div>

          <div className="mt-4">
            ©
            {' '}
            {new Date().getFullYear()}
            {' '}
            AizawlGarbo. All rights reserved.
          </div>

        </div>

      </footer>
    </div>
  )
}
