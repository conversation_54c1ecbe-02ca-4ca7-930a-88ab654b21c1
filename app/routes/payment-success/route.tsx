import { format } from 'date-fns'
import { Link, useLocation } from 'react-router'
import { buttonVariants } from '~/components/ui/button'
import { parseCurrency } from '~/lib/parse-currency'

export default function PaymentSuccess() {
  const location = useLocation()
  const paymentData = location.state

  return (
    <div className="flex h-screen w-full flex-col">
      <div className="mx-auto flex size-full max-w-4xl flex-col text-gray-500">
        <img src="./logo.jpg" className="size-42" alt="Aizawl garbo logo" />
        <h1 className="text-4xl font-bold text-green-500">Payment successful</h1>
        <div className="mt-4 flex flex-col">
          <div>
            Thank you for choosing AizawlGarbo!
          </div>
          <div>
            Your subscription fee has been successfully paid
          </div>
        </div>
        <div className="mt-4">
          <div className="font-bold">
            Payment detail:
          </div>
          <div>
            Order ID:
            {' '}
            {paymentData?.order_id || '-'}
          </div>
          <div>
            Subscription Plan:
          </div>
          <div>
            Amount:
            {' '}
            {paymentData?.payable_amount ? parseCurrency(paymentData.payable_amount) : '-'}
          </div>
        </div>
        <div className="mt-4">
          Date:
          {' '}
          {paymentData?.paid_at ? format(new Date(paymentData.paid_at), 'dd-MMMM-yyyy') : '-'}
        </div>
        {paymentData?.payment_receipt_link && (
          <a
            href={paymentData.payment_receipt_link}
            className="mt-4 inline-block"
          >
            Download receipt
          </a>
        )}
        <div className="mt-8">
          <Link to="/" className={buttonVariants({ variant: 'default' })}>
            Back to home
          </Link>

        </div>
      </div>

    </div>
  )
}
