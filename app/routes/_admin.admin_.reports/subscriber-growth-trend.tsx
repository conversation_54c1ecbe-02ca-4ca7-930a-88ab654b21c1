import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Line, LineChart, XAxis, YAxis } from 'recharts'
import LoaderIcon from '~/components/icons/loader-icon'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '~/components/ui/chart'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { generateYearOptions } from '~/lib/generate-year-options'
import { graphqlClient } from '~/lib/graphql-client'
import { parseMonth } from '~/lib/parse-month'
import { SUBSCRIBER_GROWTH_TREND } from './graphql'

const chartConfig = {
  total_subscriber: {
    label: 'Total subscriber',
    color: 'hsl(var(--chart-1))',
  },
}

export default function SubscriberGrowthTrend() {
  const [year, setYear] = useState(new Date().getFullYear())
  const yearOptions = generateYearOptions()

  const { data, isLoading, isError } = useQuery({
    queryKey: ['subscriber-growth-trend', year],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: SUBSCRIBER_GROWTH_TREND,
        variables: {
          year,
        },
      })
    },
  })

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoaderIcon className="animate-spin" />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex h-full items-center justify-center text-lg">
        Error. Unable to get data
      </div>
    )
  }

  const chartData = data?.subscriberGrowthTrend?.map(item => ({
    month: parseMonth(item.month),
    total_subscriber: item.total_subscriber,
  })) || []

  return (
    <>
      <div className="flex size-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <div className="text-lg font-semibold">
            Subscriber growth trend
          </div>
          <Select value={year.toString()} onValueChange={value => setYear(Number.parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Select year" />
            </SelectTrigger>
            <SelectContent>
              {yearOptions.map(yearOption => (
                <SelectItem key={yearOption} value={yearOption.toString()}>
                  {yearOption}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <ChartContainer
          config={chartConfig}
          className="size-full"
        >
          <LineChart
            accessibilityLayer
            data={chartData}
            margin={{
              top: 20,
              right: 20,
              left: -20,
              bottom: 40,
            }}
          >
            <XAxis dataKey="month" />
            <YAxis />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line
              dataKey="total_subscriber"
              type="natural"
              strokeWidth={2}
            />
          </LineChart>
        </ChartContainer>
      </div>
    </>
  )
}
