import type { Zone } from '~/gql/graphql'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { LabelL<PERSON>, <PERSON>, Pie<PERSON>hart } from 'recharts'
import LoaderIcon from '~/components/icons/loader-icon'
import { ChartContainer, ChartLegend, ChartLegendContent, ChartTooltip, ChartTooltipContent } from '~/components/ui/chart'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { graphqlClient } from '~/lib/graphql-client'
import { SUBSCRIBER_PLAN_DISTRIBUTION } from './graphql'

interface Props {
  zones: Zone[]
}

export default function SubscriptionPlanDistribution({ zones }: Props) {
  const [zoneId, setZoneId] = useState('')

  const { data, isLoading, isError } = useQuery({
    queryKey: ['subscription-plan-distribution', zoneId],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: SUBSCRIBER_PLAN_DISTRIBUTION,
        variables: {
          zone_id: zoneId || undefined,
        },
      })
    },
  })

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoaderIcon className="animate-spin" />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex h-full items-center justify-center text-lg">
        Error. Unable to get data
      </div>
    )
  }

  const chartData = data?.subscriberPlanDistribution?.filter(item => item.total_subscriber > 0).map((item, index) => ({
    plan_name: item.plan_name,
    total_subscriber: item.total_subscriber,
    fill: `var(--chart-${(index % 5) + 1})`,
  })) || []

  const chartConfig = chartData.reduce((config, item, index) => {
    config[item.plan_name] = {
      label: `${item.plan_name} - ${item.total_subscriber} subscribers`,
      color: `var(--chart-${(index % 5) + 1})`,
    }
    return config
  }, {} as any)

  return (
    <div>
      <div className="text-lg font-semibold">
        Subscriber plan distribution
      </div>

      <div className="my-4 flex gap-x-4">
        <Select value={zoneId} onValueChange={value => setZoneId(value === 'All' ? '' : value)}>
          <SelectTrigger className="w-full bg-white">
            <SelectValue placeholder="Search by zone" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All zones</SelectItem>
            {zones.map((zone) => {
              return <SelectItem key={zone.id} value={zone.id}>{zone.name}</SelectItem>
            })}
          </SelectContent>
        </Select>
      </div>
      <div>
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie data={chartData} dataKey="total_subscriber" nameKey="plan_name">
              <LabelList
                dataKey="plan_name"
                className="fill-background"
              />

            </Pie>
            <ChartLegend
              content={<ChartLegendContent nameKey="plan_name" />}
              className={`
                flex -translate-y-2 flex-col items-start gap-2
                [&>*]:basis-1/4 [&>*]:justify-center
              `}
            />
          </PieChart>
        </ChartContainer>
      </div>
    </div>
  )
}
