import type { Plan, Zone } from '~/gql/graphql'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Line, LineChart, <PERSON>Axis, YAxis } from 'recharts'
import LoaderIcon from '~/components/icons/loader-icon'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '~/components/ui/chart'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { months } from '~/lib/constants'
import { generateYearOptions } from '~/lib/generate-year-options'
import { graphqlClient } from '~/lib/graphql-client'
import { SUBSCRIPTION_PAYMENT_TREND } from './graphql'

interface Props {
  zones: Zone[]
  plans: Plan[]
}

const chartConfig = {
  total_amount: {
    label: 'Total amount',
    color: 'hsl(var(--chart-1))',
  },
}

export default function SubscriptionFeePaymentTrend({ zones, plans }: Props) {
  const [year, setYear] = useState(new Date().getFullYear())
  const [month, setMonth] = useState(new Date().getMonth() + 1)
  const [planId, setPlanId] = useState('')
  const [zoneId, setZoneId] = useState('')

  const yearOptions = generateYearOptions()

  const { data, isLoading, isError } = useQuery({
    queryKey: ['subscription-fee-payment-trend', year, month, planId, zoneId],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: SUBSCRIPTION_PAYMENT_TREND,
        variables: {
          month,
          year,
          plan_id: planId || undefined,
          zone_id: zoneId || undefined,
        },
      })
    },
  })

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoaderIcon className="animate-spin" />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex h-full items-center justify-center text-lg">
        Error. Unable to get data
      </div>
    )
  }

  const chartData = data?.subscriptionPaymentTrend?.map(item => ({
    day: item.day,
    total_amount: item.total_amount,
  })) || []

  return (
    <div>
      <div className="text-lg font-semibold">
        Subscription fee payment trend
      </div>

      <div className="my-4 flex gap-x-4">
        <Select value={year.toString()} onValueChange={value => setYear(Number.parseInt(value))}>
          <SelectTrigger className="w-32 bg-white">
            <SelectValue placeholder="Select year" />
          </SelectTrigger>
          <SelectContent>
            {yearOptions.map(yearOption => (
              <SelectItem key={yearOption} value={yearOption.toString()}>
                {yearOption}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={month.toString()} onValueChange={value => setMonth(Number.parseInt(value))}>
          <SelectTrigger className="w-40 bg-white">
            <SelectValue placeholder="Select month" />
          </SelectTrigger>
          <SelectContent>
            {months.map(monthOption => (
              <SelectItem key={monthOption.value} value={monthOption.value.toString()}>
                {monthOption.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={planId} onValueChange={value => setPlanId(value === 'All' ? '' : value)}>
          <SelectTrigger className="w-full bg-white">
            <SelectValue placeholder="Search by plan" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All plans</SelectItem>
            {plans.map((plan) => {
              return <SelectItem key={plan.id} value={plan.id}>{plan.name}</SelectItem>
            })}
          </SelectContent>
        </Select>

        <Select value={zoneId} onValueChange={value => setZoneId(value === 'All' ? '' : value)}>
          <SelectTrigger className="w-full bg-white">
            <SelectValue placeholder="Search by zone" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All zones</SelectItem>
            {zones.map((zone) => {
              return <SelectItem key={zone.id} value={zone.id}>{zone.name}</SelectItem>
            })}
          </SelectContent>
        </Select>
      </div>

      <div className="mt-4">
        <ChartContainer
          config={chartConfig}
          className="size-full"
        >
          <LineChart
            accessibilityLayer
            data={chartData}
            margin={{
              top: 20,
              right: 20,
              left: -20,
              bottom: 40,
            }}
          >
            <XAxis dataKey="day" />
            <YAxis />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line
              dataKey="total_amount"
              type="natural"
              strokeWidth={2}
            />
          </LineChart>
        </ChartContainer>
      </div>
    </div>
  )
}
