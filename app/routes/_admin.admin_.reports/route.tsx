import type { Route } from './+types/route'
import { useLoaderData } from 'react-router'
import PageHeader from '~/components/common/page-header'
import { GET_ZONES_AND_PLANS } from '~/graphql/queries/get-zones-and-plans'
import { graphqlClient } from '~/lib/graphql-client'
import { getSession } from '~/sessions'
import BasicStats from './basic-stats'
import SubscriberGrowthTrend from './subscriber-growth-trend'
import SubscriptionFeePaymentTrend from './subscription-fee-payment-trend'
import SubscriptionPlanDistribution from './subscription-plan-distribution'

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get('Cookie'))
  const token = session.get('token')
  const client = await graphqlClient({ token })

  const data = await client.request({
    document: GET_ZONES_AND_PLANS,
  })

  const zones = data.getZones || []
  const plans = data.getPlans || []

  return {
    zones,
    plans,
  }
}

export default function Reports() {
  const { zones, plans } = useLoaderData<typeof loader>()

  return (
    <div className="flex grow flex-col gap-4">
      <PageHeader title="Reports" />
      <div className="grid grid-cols-5 gap-8">
        <div className="col-span-3 h-92 w-full rounded-md border bg-white p-4">
          <BasicStats />
        </div>
        <div className="col-span-2 h-92 rounded-md border bg-white p-4">
          <SubscriberGrowthTrend />
        </div>
        <div className="col-span-3 w-full rounded-md border bg-white p-4">
          <SubscriptionFeePaymentTrend zones={zones} plans={plans} />
        </div>
        <div className="col-span-2 rounded-md border bg-white p-4">
          <SubscriptionPlanDistribution zones={zones} />
        </div>
      </div>
    </div>
  )
}
