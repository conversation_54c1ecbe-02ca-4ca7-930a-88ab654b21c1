import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { useState } from 'react'
import LoaderIcon from '~/components/icons/loader-icon'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { graphqlClient } from '~/lib/graphql-client'
import { parseCurrency } from '~/lib/parse-currency'
import { GET_BASICS_STATS } from './graphql'

export default function BasicStats() {
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  const handleStartDate = (e: React.ChangeEvent<HTMLInputElement>) => {
    setStartDate(e.target.value)
  }

  const handleEndDate = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEndDate(e.target.value)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-basic-stats', startDate, endDate],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_BASICS_STATS,
        variables: {
          start_date: startDate ? format(new Date(startDate), 'yyyy-MM-dd hh:mm:ss') : undefined,
          end_date: endDate ? format(new Date(endDate), 'yyyy-MM-dd hh:mm:ss') : undefined,
        },

      })
    },
  })

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoaderIcon className="animate-spin" />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex h-full items-center justify-center text-lg">
        Error. Unable to get data
      </div>
    )
  }

  return (
    <div className="grid size-full grid-cols-4 items-center gap-4">
      <div className="col-span-4 grid grid-cols-4 gap-x-4">
        <Label className="col-span-1 flex flex-col items-start gap-y-2">
          <div>Start Date</div>
          <Input
            name="startDate"
            placeholder="Search by start date"
            className="block"
            type="date"
            value={startDate}
            onChange={handleStartDate}
          />
        </Label>
        <Label className="col-span-1 flex flex-col items-start gap-y-2">
          <div>End Date</div>
          <Input
            name="endDate"
            placeholder="Search by end date"
            className="block"
            type="date"
            value={endDate}
            onChange={handleEndDate}
          />
        </Label>

      </div>
      <div className="col-span-1">
        <div
          className={`
            relative flex h-44 flex-col items-center justify-center rounded-md
            bg-blue-100
          `}
        >
          <div className="text-2xl font-bold">
            {data?.getBasicStats.total_subscriber}
          </div>

          <div className="absolute bottom-2 w-1/2 border-3 border-yellow-500" />
        </div>
        <div className="mt-4 text-center">Total subscribers</div>
      </div>

      <div className="col-span-1">
        <div
          className={`
            relative flex h-44 flex-col items-center justify-center rounded-md
            bg-blue-100
          `}
        >
          <div className="text-2xl font-bold">
            {parseCurrency((data?.getBasicStats.total_subscription_fee as number) || 0)}
          </div>

          <div className="absolute bottom-2 w-1/2 border-3 border-blue-500" />
        </div>
        <div className="mt-4 text-center">Total subscription fee</div>
      </div>

      <div className="col-span-1">
        <div
          className={`
            relative flex h-44 flex-col items-center justify-center rounded-md
            bg-blue-100
          `}
        >
          <div className="text-2xl font-bold">
            {parseCurrency((data?.getBasicStats.total_fee_paid as number) || 0)}
          </div>

          <div className="absolute bottom-2 w-1/2 border-3 border-green-500" />
        </div>
        <div className="mt-4 text-center">Total fee paid</div>
      </div>

      <div className="col-span-1">
        <div
          className={`
            relative flex h-44 flex-col items-center justify-center rounded-md
            bg-blue-100
          `}
        >
          <div className="text-2xl font-bold">
            {parseCurrency((data?.getBasicStats.total_fee_unpaid as number) || 0)}
          </div>

          <div className="absolute bottom-2 w-1/2 border-3 border-red-500" />
        </div>
        <div className="mt-4 text-center">Total fee unpaid</div>
      </div>
    </div>
  )
}
