import { graphql } from '~/gql'

export const SUBSCRIBER_PLAN_DISTRIBUTION = graphql(`
  query SubscriberPlanDistribution($zone_id: ID) {
    subscriberPlanDistribution(zone_id: $zone_id) {
      plan_name
      total_subscriber
    }
  }
`)

export const SUBSCRIBER_GROWTH_TREND = graphql(`
  query SubscriberGrowthTrend($year: Int!) {
    subscriberGrowthTrend(year: $year) {
      month
      total_subscriber
    }
  }
`)

export const SUBSCRIPTION_PAYMENT_TREND = graphql(`
  query SubscriptionPaymentTrend($month: Int!, $year: Int!, $plan_id: ID, $zone_id: ID) {
    subscriptionPaymentTrend(month: $month, year: $year, plan_id: $plan_id, zone_id: $zone_id) {
      day
      total_amount
    }
  }
`)

export const GET_BASICS_STATS = graphql(`
  query GetBasicStats($start_date: DateTime, $end_date: DateTime) {
    getBasicStats(start_date: $start_date, end_date: $end_date) {
      total_subscriber
      total_subscription_fee
      total_fee_paid
      total_fee_unpaid
    }
  }
`)
