import type { Route } from './+types/route'
import { ClientError } from 'graphql-request'
import { useEffect } from 'react'
import { data, useLoaderData, useNavigate } from 'react-router'
import { graphqlClient } from '~/lib/graphql-client'
import { handleRazorpay } from '~/lib/handle-razorpay'
import { loadRazorpayScript, removeRazorpayScript } from '~/lib/load-razorpay'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { PROCESS_SUBSCRIPTION_PAYMENT_LINK } from './graphql'

export async function loader({ params }: Route.LoaderArgs) {
  const hash = params.hash
  const client = await graphqlClient()

  try {
    const result = await client.request({
      document: PROCESS_SUBSCRIPTION_PAYMENT_LINK,
      variables: {
        payment_link_hash: hash,
      },
    })

    if (!result) {
      throw data('Payment link not found', { status: 404 })
    }

    return { data: result }
  }
  catch (error) {
    if (error instanceof ClientError) {
      if (error.response?.status === 404) {
        throw data('Payment link not found', { status: 404 })
      }
      if (error.response?.status === 401) {
        throw data('Unauthorized', { status: 401 })
      }

      throw data(parseGraphqlError(error), { status: 500 })
    }

    throw error
  }
}

export default function ProcessPayment() {
  const { data } = useLoaderData<typeof loader>()
  const navigate = useNavigate()

  useEffect(() => {
    if (data?.processSubscriptionPaymentLink) {
      (async () => {
        await loadRazorpayScript()
        handleRazorpay({
          orderId: data.processSubscriptionPaymentLink.order_id,
          navigate,
        })
      })()
    }

    return () => {
      removeRazorpayScript()
    }
  }, [data, navigate])

  return (
    <div className="flex h-screen w-full flex-col items-center justify-center">
      <h1>Processing payment</h1>
    </div>
  )
}
