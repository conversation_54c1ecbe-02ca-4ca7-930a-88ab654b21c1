import { graphql } from '~/gql'

export const PROCESS_SUBSCRIPTION_PAYMENT_LINK = graphql(`
  mutation ProcessSubscriptionPaymentLink(
    $payment_link_hash: String!
  ) {
    processSubscriptionPaymentLink(
      payment_link_hash: $payment_link_hash
    ) {
      ... on RzpayPaymentOrder {
        id
        order_id
        payment_error
        payment_id
        payment_instrument_type
        payment_status
        refund_amount
        refund_error
        refund_status
      }
    }
  }
`)
