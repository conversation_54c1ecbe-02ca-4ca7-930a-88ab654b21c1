import { graphql } from '~/gql'

export const GET_COLLECTION_SENT_NOTIFICATION_HISTORIES = graphql(`
  query GetCollectionSentNotificationHistories(
    $first: Int!
    $page: Int
    $week_day: String
    $sent_date: DateTime
  ) {
    getCollectionSentNotificationHistories(first: $first, page: $page, week_day: $week_day, sent_date: $sent_date) {
      data {
        id
        establishment {
          id
          name
          address
          phone_number
          alternate_phone_number: alt_phone_number
          plan {
            id
            name
          }
          collection_days
          # zone {
          #   id
          #   name
          # }
          # currentPlan {
          #   id
          #   name
          # }
          # collection_day
        }
        week_day
        latestWhatsApp {
          id
          message_status
        }
      }
      paginator_info {
        total
        current_page
        last_page
      }
    }
  }
`)
