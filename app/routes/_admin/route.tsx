import { Outlet } from 'react-router'
import { SidebarProvider, SidebarTrigger } from '~/components/ui/sidebar'
import AppSidebar from './app-sidebar'

export default function HomeLayout() {
  return (
    <div
      className={`
        mx-auto flex size-full min-h-screen w-full flex-col overflow-hidden
      `}
    >
      <SidebarProvider>
        <div className="flex grow overflow-auto">
          <AppSidebar />
          <main className="flex grow flex-col bg-gray-100 p-2">
            <SidebarTrigger />
            <div className="mx-auto mt-4 flex w-full grow flex-col">
              <Outlet />
            </div>
          </main>
        </div>
      </SidebarProvider>
    </div>
  )
}
