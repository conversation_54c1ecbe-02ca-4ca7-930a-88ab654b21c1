import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, useQueryState } from 'nuqs'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_COLLECTION_SCHEDULE_NOTIFICATION_STATS } from './graphql'

export default function useGetCollectionScheduleNotificationStats() {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePage = (page: number) => {
    setPage(page)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-collection-schedule-notification-stats', page],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_COLLECTION_SCHEDULE_NOTIFICATION_STATS,
        variables: {
          first: 15,
          page,
        },
      })
    },
  })

  const lastPage = data?.getCollectionScheduleNotificationStats?.paginator_info?.last_page || 1

  return { data, isLoading, isError, page, handlePage, lastPage }
}
