import { Link } from 'react-router'
import PageHeader from '~/components/common/page-header'
import { buttonVariants } from '~/components/ui/button'
import { weekdays } from '~/lib/constants'
import { cn } from '~/lib/utils'
import CollectionScheduleNotificationStatsTable from './collection-schedule-notification-stats-table'

export default function CollectionSchedules() {
  return (
    <div className="flex grow flex-col gap-4">
      <PageHeader title="Collection Schedules" />
      <div className="flex flex-col">
        <div className="w-full border-b border-gray-500 pb-2">
          Collection List by Day
        </div>
        <div className="my-8 flex gap-8">
          {weekdays.map(weekday => (
            <Link
              to={`/admin/collection-schedules/${weekday.value.toLowerCase()}`}
              key={weekday.value}
              className={cn(buttonVariants({ variant: 'outline' }), `
                aspect-video h-full w-full shrink-1 bg-white text-2xl font-bold
                text-black
              `)}
            >
              {weekday.label.slice(0, 3)}
            </Link>
          ))}
        </div>
      </div>

      <div className="w-full border-b border-gray-500 pb-2">
        Notification History
      </div>
      <CollectionScheduleNotificationStatsTable />
    </div>
  )
}
