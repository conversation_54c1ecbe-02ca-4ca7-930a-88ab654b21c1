import type { Route } from './+types/route'
import { data, redirect, useFetcher } from 'react-router'
import { toast } from 'sonner'
import { z } from 'zod'
import { useAppForm } from '~/hooks/form'
import useAdminLogin from '~/hooks/use-admin-login'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { commitSession, destroySession, getSession } from '~/sessions'

const schema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
})

export async function action({ request }: Route.ActionArgs) {
  const session = await getSession(request.headers.get('Cookie'))
  const formData = await request.formData()

  const role = formData.get('role')
  const exp = formData.get('exp')
  const token = formData.get('token')

  if (!role || !exp || !token) {
    return data(
      { error: 'Invalid session data' },
      {
        headers: {
          'Set-Cookie': await destroySession(session),
        },
      },
    )
  }

  session.set('role', role.toString())
  session.set('token', token.toString())

  const expires = new Date(exp.toString().replace(' ', 'T'))

  const cookie = await commitSession(session, {
    expires,
  })

  if (role === 'admin') {
    return redirect('/admin/customers', {
      headers: {
        'Set-Cookie': cookie,
      },
    })
  }
  else if (role === 'staff') {
    return redirect('/staff', {
      headers: {
        'Set-Cookie': cookie,
      },
    })
  }
}

export default function Login() {
  const { adminLogin } = useAdminLogin()
  const fetcher = useFetcher()

  const form = useAppForm({
    defaultValues: {
      username: '',
      password: '',
    },
    validators: {
      onSubmit: schema,
    },
    onSubmit: async ({ value }) => {
      await adminLogin.mutateAsync(value, {
        onSuccess: (data) => {
          const formData = new FormData()
          const role = data.adminLogin?.user.role || ''
          const exp = data.adminLogin?.exp || ''
          const token = data.adminLogin?.token || ''

          formData.append('role', role)
          formData.append('exp', exp)
          formData.append('token', token)

          fetcher.submit(formData, {
            method: 'POST',
          })
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
          console.error(error)
        },
      })
    },
  })

  return (
    <div className="flex h-screen w-full flex-col items-center justify-center">
      <h1 className="text-xl font-bold">Welcome</h1>
      <div>Please login to continue</div>
      <form
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
        className="mt-4 flex w-full max-w-72 flex-col gap-y-6"
      >
        <form.AppField
          name="username"
          children={field => <field.InputField label="Name" />}
        />
        <form.AppField
          name="password"
          children={field => <field.InputField label="Password" type="password" />}
        />
        <form.AppForm>
          <form.SubmitButton label="Login" />
        </form.AppForm>
      </form>
    </div>
  )
}
