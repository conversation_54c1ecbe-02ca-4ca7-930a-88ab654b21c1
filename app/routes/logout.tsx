import type { Route } from './+types/logout'

import { redirect } from 'react-router'

import { destroySession, getSession } from '~/sessions'

async function handleLogout(request: Request) {
  const session = await getSession(request.headers.get('<PERSON>ie'))

  return redirect('/login', {
    headers: {
      'Set-Cookie': await destroySession(session),
    },
  })
}

export async function action({ request }: Route.ActionArgs) {
  return handleLogout(request)
}

export async function loader({ request }: Route.LoaderArgs) {
  return handleLogout(request)
}
