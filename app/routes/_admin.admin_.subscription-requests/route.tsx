import type { Route } from './+types/route'
import { useLoaderData } from 'react-router'
import PageHeader from '~/components/common/page-header'
import { GET_ZONES_AND_PLANS } from '~/graphql/queries/get-zones-and-plans'
import { graphqlClient } from '~/lib/graphql-client'
import { getSession } from '~/sessions'
import SubscriptionRequestTable from './subscription-request-table'

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get('Cookie'))

  const token = session.get('token')

  const client = await graphqlClient({ token })
  const data = await client.request({
    document: GET_ZONES_AND_PLANS,
  })

  const zones = data.getZones || []
  const plans = data.getPlans || []

  return {
    zones,
    plans,
  }
}

export default function SubscriptionRequests() {
  const { zones, plans } = useLoaderData<typeof loader>()
  return (
    <div className="flex flex-col gap-4">
      <PageHeader title="Subscription Requests" />
      <SubscriptionRequestTable zones={zones} plans={plans} />
    </div>
  )
}
