import z from 'zod'
import { CollectionDay, SubscriptionStatus } from '~/gql/graphql'

export const approveCustomerSchema = z.object({
  id: z.string().min(1, 'ID is required'),
  name: z.string().optional(),
  address: z.string().optional(),
  phone_number: z.string().optional(),
  alternate_phone_number: z.string().optional(),
  collection_days: z.array(z.enum(CollectionDay)).min(1, 'Collection days is required'),
  zone_id: z.string().min(1, 'Zone is required'),
  plan_id: z.string().optional(),
  period: z.number({ error: 'Please enter valid billing cycle' }).min(1, 'Billing cycle is required'),
  subscription_status: z.enum(SubscriptionStatus).optional(),
  customer_remarks: z.string().optional(),
  start_date: z.string().min(1, 'Start date is required'),
})

export type ApproveCustomerType = z.infer<typeof approveCustomerSchema>
