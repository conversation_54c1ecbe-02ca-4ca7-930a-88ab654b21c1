import { Button } from '~/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, <PERSON><PERSON>Header, <PERSON>alogTitle, DialogTrigger } from '~/components/ui/dialog'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import { sendCollectionScheduleNotificationSchema } from './schema'
import useGetCollectionSchedule from './use-get-collection-schedule'
import useSendCollectionScheduleNotification from './use-send-collection-schedule-notification'

interface Props {
  weekDay: string
  establishmentIds: string[]
}

export default function SendCollectionScheduleNotificationDialog({ weekDay, establishmentIds }: Props) {
  const { isOpen, toggle } = useBoolean()
  const { total } = useGetCollectionSchedule({ collectionDay: weekDay })
  const { sendCollectionScheduleNotification } = useSendCollectionScheduleNotification()

  const form = useAppForm({
    defaultValues: {
      establishment_ids: establishmentIds,
      remarks: '',
      week_day: weekDay,
    },
    validators: {
      onSubmit: sendCollectionScheduleNotificationSchema,
    },
    onSubmit: async ({ value }) => {
      await sendCollectionScheduleNotification.mutateAsync(value, {
        onSuccess: () => {
          form.reset()
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>
        <Button variant="outline">
          Send Notification
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Confirm Notification
          </DialogTitle>
          <DialogDescription>
            Selected subscribers will receive notification for collection via WhatsApp
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col text-sm">
          <div>
            Collection day:
            {' '}
            {weekDay.toUpperCase()}
          </div>
          <div>
            Additional recipients:
            {' '}
            {establishmentIds.length}
          </div>
          <div>
            Total recipients:
            {' '}
            {establishmentIds.length + total}
          </div>

        </div>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-4"
        >
          <form.AppField
            name="remarks"
            children={field => <field.TextareaField label="Remarks" />}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Confirm" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
