import type { CollectionDay } from '~/gql/graphql'
import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, useQueryState } from 'nuqs'
import { SubscriptionStatus } from '~/gql/graphql'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_COLLECTION_SCHEDULE } from './graphql'

interface Props {
  collectionDay: string
}

export default function useGetCollectionSchedule({ collectionDay }: Props) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePage = (page: number) => {
    setPage(page)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-collection-schedule', page, collectionDay],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_COLLECTION_SCHEDULE,
        variables: {
          first: 15,
          page,
          collection_day: collectionDay.toUpperCase() as CollectionDay,
          subscription_status: SubscriptionStatus.Active,
        },
      })
    },
    enabled: !!collectionDay,
  })

  const lastPage = data?.getCollectionSchedule?.paginator_info?.last_page || 1
  const total = data?.getCollectionSchedule?.paginator_info?.total || 0

  return { data, isLoading, isError, page, lastPage, handlePage, collectionDay, total }
}
