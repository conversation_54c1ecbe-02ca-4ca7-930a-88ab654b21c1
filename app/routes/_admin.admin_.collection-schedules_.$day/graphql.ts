import { graphql } from '~/gql'

export const GET_COLLECTION_SCHEDULE = graphql(`
  query GetCollectionSchedule($first: Int!, $page: Int, $collection_day: CollectionDay, $subscription_status: SubscriptionStatus) {
    getCollectionSchedule(first: $first, page: $page, collection_day: $collection_day, subscription_status: $subscription_status) {
      data {
        id
        name
        address
        phone_number
        alternate_phone_number: alt_phone_number
        plan {
          name
        }
        collection_days
        # zone {
        #   id
        #   name
        # }
        # currentPlan {
        #   id
        #   name
        # }
        # collection_day
        # customer {
        #   latestWhatsApp {
        #     id
        #     message_status
        #   }
        # }
      }
      paginator_info {
        total
        current_page
        last_page
      }
    }
  }
`)

export const SEND_COLLECTION_SCHEDULE_NOTIFICATION = graphql(`
  mutation SendCollectionScheduleNotification(
    $establishment_ids: [ID!]
    $remarks: String
    $week_day: String!
  ) {
    sendCollectionScheduleNotification(establishment_ids: $establishment_ids, remarks: $remarks, week_day: $week_day)
  }
`)

export const SEARCH_ESTABLISHMENT = graphql(`
  query SearchEstablishment(
    # $first: Int!
    # $name: String
    # $customer_type: CustomerType
    $keyword: String!
  ) {
    searchEstablishment(
      keyword: $keyword
      # first: $first
      # name: $name
      # customer_type: $customer_type
    ) {
        id
        name
        address
        phone_number
        zone {
          id
          name
        }
        collection_days
        plan {
          id
          name
        }
        latestWhatsApp {
          id
          message_status
        }
  }
}
  `)
