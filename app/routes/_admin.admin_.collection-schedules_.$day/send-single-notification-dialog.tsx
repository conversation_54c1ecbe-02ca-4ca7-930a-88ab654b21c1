import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle } from '~/components/ui/dialog'
import { useAppForm } from '~/hooks/form'
import useSendCollectionScheduleNotification from './use-send-collection-schedule-notification'

interface Props {
  weekDay: string
  establishmentIds: string
  name: string
  phoneNumber: string
  isOpen: boolean
  toggle: (open: boolean) => void
}

export default function SendSingleNotificationDialog({ isOpen, toggle, weekDay, establishmentIds, name, phoneNumber }: Props) {
  const { sendCollectionScheduleNotification } = useSendCollectionScheduleNotification()

  const form = useAppForm({
    defaultValues: {
      establishment_ids: [establishmentIds],
      remarks: '',
      week_day: weekDay,
    },
    onSubmit: async ({ value }) => {
      await sendCollectionScheduleNotification.mutateAsync(value, {
        onSuccess: () => {
          form.reset()
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Confirm Notification
          </DialogTitle>
          <DialogDescription>
            {name}
            {' '}
            {phoneNumber}
            {' '}
            will receive notification for collection via WhatsApp
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col text-sm">
          <div>
            Collection day:
            {' '}
            {weekDay.toUpperCase()}
          </div>

        </div>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-4"
        >
          <form.AppField
            name="remarks"
            children={field => <field.TextareaField label="Remarks" />}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Confirm" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
