import type { SearchEstablishmentQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { CheckIcon, ChevronsUpDown } from 'lucide-react'
import { useState } from 'react'
import { useParams } from 'react-router'
import BackButton from '~/components/common/back-button'
import { Button } from '~/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '~/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import useBoolean from '~/hooks/use-boolean'
import { cn } from '~/lib/utils'
import CollectionScheduleTable from './collection-schedule-table'
import SelectedEstablishmentsTable from './selected-establishments-table'
import SendCollectionScheduleNotificationDialog from './send-collection-schedule-notification-dialog'
import useSearchEstablishments from './use-search-establishments'

export default function CollectionScheduleByDay() {
  const { day } = useParams()
  const { isOpen, toggle } = useBoolean()

  const [selectedEstablishments, setSelectedEstablishments] = useState<SearchEstablishmentQuery['searchEstablishment']>([])

  const handleRemoveEstablishment = (id: string) => {
    setSelectedEstablishments(selectedEstablishments!.filter(c => c.id !== id))
  }

  const { keyword, handleKeywordChange, isLoading, data, isError } = useSearchEstablishments()

  const establishmentIds = selectedEstablishments?.map(c => c.id) || []

  return (
    <>
      <div className="flex grow flex-col gap-4">
        <div className="flex justify-between">
          <BackButton />
          <div>
            {format(new Date(), 'EEEE, do MMM, yyyy')}
            {' '}
            (Current Date)
          </div>
        </div>

        <div className="my-4 w-full border border-gray-500" />

        <div className="flex justify-between">
          <Popover open={isOpen} onOpenChange={toggle}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={isOpen}
              // className="w-[200px] justify-between"
              >
                Search establishment
                <ChevronsUpDown className="opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full min-w-96 p-0" align="start">
              <Command className="w-full">
                <CommandInput
                  isLoading={isLoading}
                  value={keyword}
                  onValueChange={handleKeywordChange}
                  placeholder="Search customers..."
                />
                <CommandList className="w-full">
                  {!isLoading && <CommandEmpty>No customer found.</CommandEmpty>}
                  {isError && (
                    <div className="py-6 text-center text-sm">
                      Error fetching customer list.
                    </div>
                  )}
                  <CommandGroup>
                    {data?.searchEstablishment?.map(establishment => (
                      <CommandItem
                        key={establishment.id}
                        value={establishment.name}
                        onSelect={() => {
                          if (selectedEstablishments?.some(c => c.id === establishment.id))
                            return
                          setSelectedEstablishments(selectedEstablishments?.concat(establishment))
                          toggle(false)
                        }}
                      >
                        <CheckIcon
                          className={cn(
                            'mr-2 h-4 w-4',
                            selectedEstablishments?.some(c => c.id === establishment.id)
                              ? `opacity-100`
                              : `opacity-0`,
                          )}
                        />
                        <div className="flex flex-col text-xs">
                          <div>
                            {establishment.name}
                            {' / '}
                            {establishment.phone_number}
                            {' / '}
                            {establishment.address}
                            {' / '}
                            {/* {customer.zone?.name} */}
                          </div>
                          <div>
                            Collection days:
                            {' '}
                            {/* {customer.collection_day.map(d => d.slice(0, 3)).join(', ')} */}
                          </div>

                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>

          <SendCollectionScheduleNotificationDialog establishmentIds={establishmentIds} weekDay={day!} />
        </div>
        {selectedEstablishments && selectedEstablishments.length > 0 && (
          <SelectedEstablishmentsTable establishments={selectedEstablishments} onRemove={handleRemoveEstablishment} />
        )}
        <CollectionScheduleTable collectionDay={day!} />
      </div>
    </>
  )
}
