import { useState } from 'react'
import PagePagination from '~/components/common/page-pagination'
import MessageIcon from '~/components/icons/message-icon'
import { Button } from '~/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import useBoolean from '~/hooks/use-boolean'
import SendSingleNotificationDialog from './send-single-notification-dialog'
import useGetCollectionSchedule from './use-get-collection-schedule'

interface Props {
  collectionDay: string
}

export default function CollectionScheduleTable({ collectionDay }: Props) {
  const { data, isLoading, isError, page, lastPage, handlePage } = useGetCollectionSchedule({ collectionDay })
  const { isOpen, toggle } = useBoolean()
  const [selectedEstablishment, setSelectedEstablishment] = useState({
    id: '',
    name: '',
    phone_number: '',
  })

  return (
    <>
      <div className={`
        mt-4 flex w-full grow flex-col overflow-x-auto rounded-md bg-white p-2
      `}
      >
        <div className="py-2 text-center text-sm text-gray-500">
          Collection schedule for
          {' '}
          {' '}
          {collectionDay.toUpperCase()}
        </div>
        <Table className="w-full min-w-[1000px] table-fixed caption-top">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[150px]">Name</TableHead>
              <TableHead className="w-[150px]">Phone number</TableHead>
              <TableHead className="w-[150px]">Address</TableHead>
              <TableHead className="w-[250px]">Subscription Plan</TableHead>
              <TableHead className="w-[250px]">Collection Days</TableHead>
              <TableHead className="w-[200px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading
              ? (
                  <TableRow>
                    <TableCell colSpan={6} className="py-8 text-center">
                      Loading...
                    </TableCell>
                  </TableRow>
                )
              : isError
                ? (
                    <TableRow>
                      <TableCell
                        colSpan={6}
                        className="py-8 text-center text-red-500"
                      >
                        Error loading data
                      </TableCell>
                    </TableRow>
                  )
                : data?.getCollectionSchedule?.data?.length === 0
                  ? (
                      <TableRow>
                        <TableCell colSpan={6} className="py-8 text-center">
                          No records found
                        </TableCell>
                      </TableRow>
                    )
                  : (
                      data?.getCollectionSchedule?.data?.map(record => (
                        <TableRow key={record.id}>
                          <TableCell className="truncate">
                            {record.name || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {record.phone_number || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {record.address || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {record.plan?.name || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {record.collection_days?.join(', ') || '-'}
                          </TableCell>
                          <TableCell>
                            <div className="flex justify-end gap-x-4">
                              <Button
                                size="icon"
                                variant="outline"
                                onClick={() => {
                                  setSelectedEstablishment({
                                    id: record.id,
                                    name: record.name,
                                    phone_number: record.phone_number,
                                  })
                                  toggle(true)
                                }}
                              >
                                <MessageIcon />
                              </Button>

                            </div>
                          </TableCell>
                        </TableRow>
                      )))}

          </TableBody>
        </Table>
      </div>
      {lastPage > 1 && (
        <div className="flex justify-center">
          <PagePagination
            lastPage={lastPage}
            currentPage={page}
            handlePagePagination={handlePage}
          />
        </div>
      )}
      {selectedEstablishment.id && (
        <SendSingleNotificationDialog
          establishmentIds={selectedEstablishment.id}
          name={selectedEstablishment.name}
          phoneNumber={selectedEstablishment.phone_number}
          weekDay={collectionDay}
          isOpen={isOpen}
          toggle={toggle}
        />
      )}
    </>
  )
}
