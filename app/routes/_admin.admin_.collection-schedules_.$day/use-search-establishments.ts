import { useQuery } from '@tanstack/react-query'
import { parseAsString, useQueryState } from 'nuqs'
import { useDebounce } from 'use-debounce'
import { graphqlClient } from '~/lib/graphql-client'
import { SEARCH_ESTABLISHMENT } from './graphql'

export default function useSearchCustomers() {
  const [keyword, setKeyword] = useQueryState('keyword', parseAsString.withDefault(''))

  const [debouncedKeyword] = useDebounce(keyword, 500)

  const handleKeywordChange = (e: string) => {
    setKeyword(e)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['search-establishments', debouncedKeyword],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: SEARCH_ESTABLISHMENT,
        variables: {
          keyword: debouncedKeyword,
        },
      })
    },
    enabled: !!debouncedKeyword,
  })

  return {
    data,
    isLoading,
    isError,
    keyword,
    handleKeywordChange,
  }
}
