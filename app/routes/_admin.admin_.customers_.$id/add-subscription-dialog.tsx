import type { AddSubscriptionType } from './schema'
import type { Plan, Zone } from '~/gql/graphql'
import FormMessage from '~/components/common/form-message'
import MonthYearPicker from '~/components/common/month-year-picker'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { CollectionDay } from '~/gql/graphql'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import { addSubscriptionSchema } from './schema'
import useAddSubscription from './use-add-subscription'

interface Props {
  customer_id: string
  zones: Zone[]
  plans: Plan[]
}

export default function AddSubscriptionDialog({ customer_id, plans, zones }: Props) {
  const { addSubscription } = useAddSubscription()
  const { isOpen, toggle } = useBoolean()

  const form = useAppForm({
    defaultValues: {
      customer_id,
      establishment_input: {
        alt_phone_number: '',
        collection_days: [],
        name: '',
        phone_number: '',
        address: '',
        remarks: '',
        zone_id: '',
        current_plan_id: '', // for adding subscription this one must be used when selecting plan ids
      },
      subscription_input: {
        period: 1,
        // plan_id: '', // not needed when adding subscription, using the establishment_input.current_plan_id instead
        remarks: '',
        start_date: '',
      },
    } as AddSubscriptionType,
    validators: {
      onSubmit: addSubscriptionSchema,
    },
    onSubmit: async ({ value }) => {
      await addSubscription.mutateAsync(value, {
        onSuccess: () => {
          form.reset()
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>
        <Button variant="outline">
          Add Subscription
        </Button>
      </DialogTrigger>
      <DialogContent className="w-full min-w-4xl">
        <DialogHeader>
          <DialogTitle>
            Add new subscription
          </DialogTitle>
          <DialogDescription>
            Enter subscription details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="grid grid-cols-2 gap-x-8 gap-y-4"
        >
          <div className="col-span-1">
            <form.AppField
              name="establishment_input.name"
              children={field => <field.InputField label="Name" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="establishment_input.address"
              children={field => <field.InputField label="Address" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="establishment_input.phone_number"
              children={field => <field.MobileInputField label="Phone number" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="establishment_input.alt_phone_number"
              children={field => <field.MobileInputField label="Alternate Phone number" />}
            />
          </div>

          <div className="col-span-2 my-4 border" />
          <div className="col-span-1">
            <form.AppField
              name="subscription_input.period"
              children={field => <field.NumberField label="Billing Cycle (in months)" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="subscription_input.start_date"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>
                      Start Date
                    </div>
                    <MonthYearPicker value={field.state.value} onChange={field.handleChange} />
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="establishment_input.current_plan_id"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Subscription Plan</div>
                    <Select value={field.state.value} onValueChange={field.handleChange}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {plans.map((plan) => {
                          return <SelectItem key={plan.id} value={plan.id}>{plan.name}</SelectItem>
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="establishment_input.zone_id"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Zone</div>
                    <Select value={field.state.value} onValueChange={field.handleChange}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {zones.map((zone) => {
                          return <SelectItem key={zone.id} value={zone.id}>{zone.name}</SelectItem>
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>
          <div className="col-span-2">
            <form.AppField
              name="establishment_input.remarks"
              children={field => <field.TextareaField label="Remarks" />}
            />
          </div>

          <div className="col-span-2 my-4 border" />

          <div className="col-span-2">
            <form.AppField
              name="establishment_input.collection_days"
              children={field => (
                <div className="flex flex-col gap-y-2">
                  <Label>Collection Days</Label>
                  <div className="grid grid-cols-7 gap-2">
                    <field.CheckboxInputField
                      label="Mon"
                      value={CollectionDay.Monday}
                    />
                    <field.CheckboxInputField
                      label="Tue"
                      value={CollectionDay.Tuesday}
                    />
                    <field.CheckboxInputField
                      label="Wed"
                      value={CollectionDay.Wednesday}
                    />
                    <field.CheckboxInputField
                      label="Thu"
                      value={CollectionDay.Thursday}
                    />
                    <field.CheckboxInputField
                      label="Fri"
                      value={CollectionDay.Friday}
                    />
                    <field.CheckboxInputField
                      label="Sat"
                      value={CollectionDay.Saturday}
                    />
                    <field.CheckboxInputField
                      label="Sun"
                      value={CollectionDay.Sunday}
                    />
                  </div>
                  <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                </div>
              )}
            />
          </div>
          <DialogFooter className="col-span-2 mt-2">
            <form.AppForm>
              <form.SubmitButton label="Add Subscription" />
            </form.AppForm>
          </DialogFooter>
        </form>

      </DialogContent>
    </Dialog>
  )
}
