import type { SelectedEstablishment } from './schema'
import type { Plan, Zone } from '~/gql/graphql'
import { EllipsisVertical } from 'lucide-react'
import { useState } from 'react'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import { Button } from '~/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuTrigger } from '~/components/ui/dropdown-menu'
import { Skeleton } from '~/components/ui/skeleton'
import useBoolean from '~/hooks/use-boolean'
import { capitalizeFirstLetter } from '~/lib/capitalize-first-letter'
import UpdateEstablishmentDialog from './update-establishment-dialog'
import useDeleteEstablishment from './use-delete-establishment'
import useGetCustomerEstablishments from './use-get-customer-establishments'

interface Props {
  id: string
  zones: Zone[]
  plans: Plan[]

}

function EstablishmentDetails({ title, value }: { title: string, value: string }) {
  return (
    <div className="flex justify-between gap-x-4 text-sm">
      <div className="flex w-1/3 justify-between font-bold">
        <span>{title}</span>
        <span>:</span>
      </div>
      <div className="w-2/3">
        {value}
      </div>
    </div>
  )
}

export default function CustomerEstablishments({ id, zones, plans }: Props) {
  const { isLoading, isError, establishments } = useGetCustomerEstablishments(id)
  const { deleteEstablishment } = useDeleteEstablishment()

  const { isOpen: updateOpen, toggle: toggleUpdate } = useBoolean()
  const { isOpen: deleteOpen, toggle: toggleDelete } = useBoolean()

  const [selectedEstablishment, setSelectedEstablishment] = useState<SelectedEstablishment>()

  const [selectedId, setSelectedId] = useState('')

  const handleDelete = async () => {
    if (!selectedId)
      return
    await deleteEstablishment.mutateAsync(selectedId, {
      onSuccess: () => {
        toggleDelete(false)
      },
    })
  }

  if (isLoading) {
    return (
      <div>
        <div className="flex w-1/5 flex-col gap-y-4 rounded-md bg-white p-4">
          <Skeleton className="h-8 w-1/2" />
          <Skeleton className="h-8" />
          <Skeleton className="h-8 w-1/2" />
          <Skeleton className="h-8" />
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="bg-white p-4">Error. Cannot load establishments list</div>
    )
  }

  return (
    <>
      <div className="grid grid-cols-4 gap-4">
        {establishments.map(establishment => (
          <div
            key={establishment.id}
            className="relative flex flex-col gap-y-2 rounded-md bg-white p-4"
          >
            <div className="absolute top-2 right-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <EllipsisVertical />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuGroup>
                    <DropdownMenuItem>
                      <Button
                        className="w-full"
                        type="button"
                        variant="ghost"
                        onClick={() => {
                          setSelectedEstablishment(establishment)
                          toggleUpdate(true)
                        }}
                      >
                        Update
                      </Button>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Button
                        className="w-full"
                        type="button"
                        variant="ghost"
                        onClick={() => {
                          setSelectedId(establishment.id)
                          toggleDelete(true)
                        }}
                      >
                        Delete
                      </Button>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>

                </DropdownMenuContent>
              </DropdownMenu>

            </div>
            <EstablishmentDetails title="Name" value={establishment.name} />
            <EstablishmentDetails title="Address" value={establishment.address} />
            <EstablishmentDetails title="Phone number" value={establishment.phone_number} />
            <EstablishmentDetails title="Plan" value={establishment.plan.name} />
            <EstablishmentDetails title="Status" value={capitalizeFirstLetter(establishment.subscription_status)} />
          </div>
        ))}
      </div>
      {selectedEstablishment && (
        <UpdateEstablishmentDialog
          establishment={selectedEstablishment}
          isOpen={updateOpen}
          toggle={(open: boolean) => {
            toggleUpdate(open)
            setSelectedEstablishment(undefined)
          }}
          zones={zones}
          plans={plans}
        />
      )}
      {selectedId && (
        <ConfirmationDialog
          isPending={deleteEstablishment.isPending}
          handleConfirm={handleDelete}
          open={deleteOpen}
          handleOpenChange={toggleDelete}
        />
      )}
    </>
  )
}
