import type { UpdateEstablishmentType } from './schema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UPDATE_ESTABLISHMENT } from './graphql'

export default function useUpdateEstablishment() {
  const queryClient = useQueryClient()

  const updateEstablishment = useMutation({
    mutationFn: async (data: UpdateEstablishmentType) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_ESTABLISHMENT,
        variables: {
          ...data,
          id: data.id!,
        },
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['customer-establishments'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-customers'],
      })
      toast.success('Establishment updated successfully')
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { updateEstablishment }
}
