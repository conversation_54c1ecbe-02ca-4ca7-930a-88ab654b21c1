import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { DELETE_ESTABLISHMENT } from './graphql'

export default function useDeleteEstablishment() {
  const queryClient = useQueryClient()

  const deleteEstablishment = useMutation({
    mutationFn: async (id: string) => {
      const client = await graphqlClient()
      return client.request({
        document: DELETE_ESTABLISHMENT,
        variables: {
          id,
        },
      })
    },
    onSuccess: () => {
      toast.success('Establishment deleted successfully')
      queryClient.invalidateQueries({
        queryKey: ['customer-establishments'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-customers'],
      })
      queryClient.invalidateQueries({
        queryKey: ['customer-subscription-history'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { deleteEstablishment }
}
