import { useQuery } from '@tanstack/react-query'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_CUSTOMER_ESTABLISHMENTS } from './graphql'

export default function useGetCustomerEstablishments(id: string) {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['customer-establishments', id],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_CUSTOMER_ESTABLISHMENTS,
        variables: {
          customer_id: id,
        },
      })
    },
  })

  const establishments = data?.getCustomerEstablishment || []

  return { data, isLoading, isError, establishments }
}
