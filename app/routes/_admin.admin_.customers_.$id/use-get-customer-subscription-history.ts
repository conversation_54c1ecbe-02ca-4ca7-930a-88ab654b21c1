import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, useQueryState } from 'nuqs'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_CUSTOMER_SUBSCRIPTION_HISTORY } from './graphql'

export default function useGetCustomerSubscriptionHistory(id: string) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePage = (page: number) => {
    setPage(page)
  }
  const { data, isLoading, isError } = useQuery({
    queryKey: ['customer-subscription-history', id, page],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_CUSTOMER_SUBSCRIPTION_HISTORY,
        variables: {
          customer_id: id,
          first: 15,
          page,
        },
      })
    },
  })

  const lastPage = data?.getCustomerSubscriptionHistory?.paginator_info?.last_page || 1

  return { data, isLoading, isError, page, handlePage, lastPage }
}
