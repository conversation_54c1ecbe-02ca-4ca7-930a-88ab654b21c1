import { graphql } from '~/gql'

export const GENERATE_RECEIPT_LINK = graphql(`
  mutation GeneratePaymentReceiptLink(
    $id: ID!
    $phone_number: String
    $should_send: Boolean!
  ) {
    generatePaymentReceiptLink(
      id: $id
      phone_number: $phone_number
      should_send: $should_send
    )
  }
`)

export const RESEND_PAYMENT_LINK = graphql(`
  mutation ResendPaymentLink(
    $customer_id: ID
    $subscription_id: ID
    $payable_amount: Float
    $phone_number: String!
  ) {
    resendPaymentLink(
      subscription_id: $subscription_id
      phone_number: $phone_number
      customer_id: $customer_id
      payable_amount: $payable_amount
    )
  }
`)

export const MARK_AS_PAID = graphql(`
  mutation MarkAsPaid(
    $subscription_id: ID!
    $payment_mode: PaymentMode!
    $remarks: String
    $payment_date: DateTime
  ) {
    markAsPaid(
      subscription_id: $subscription_id
      payment_mode: $payment_mode
      remarks: $remarks
      payment_date: $payment_date
    )
  }
`)

export const DELETE_ESTABLISHMENT = graphql(`
  mutation DeleteEstablishment(
    $id: ID!
  ) {
    deleteEstablishment(id: $id)
  }
`)

export const ADD_SUBSCRIPTION = graphql(`
  mutation AddSubscription(
    $customer_id: ID!
    $establishment_input: EstablishmentInput!
    $subscription_input: SubscriptionInput!
  ) {
    addSubscription(
      customer_id: $customer_id
      establishment_input: $establishment_input
      subscription_input: $subscription_input
    )
  }
`)

export const GET_CUSTOMER_SUBSCRIPTION_HISTORY = graphql(`
  query GetCustomerSubscriptionHistory($customer_id: ID!, $first: Int!, $page: Int) {
    getCustomerSubscriptionHistory(customer_id: $customer_id, first: $first, page: $page) {
      data {
        id
        start_date
        end_date
        payable_amount
        customer {
          id
          name
          address
          phone_number
        }
        payment_mode
        paymentOrder {
          id
          paymentable_type
          paymentable {
            ... on RzpayPaymentOrder{
              id
              payment_status
              payment_error
            }
          }
        }
        latestWhatsApp{
          id
          message_status
        }
        paid_at
        remarks
        subscriptionItems {
          id
          plan {
            id
            name
            price
          }
          establishment {
            id
            name
            address
            phone_number
          }
          period
        }
      }
      paginator_info {
        total
        current_page
        last_page
      }
    }
  }
`)

export const GET_CUSTOMER_ESTABLISHMENTS = graphql(`
  query GetCustomerEstablishments($customer_id: ID!) {
    getCustomerEstablishment(customer_id: $customer_id) {
      id
      name
      phone_number
      alt_phone_number
      address
      remarks
      collection_days
      zone_id
      current_plan_id
      current_plan_period
      subscription_status
      plan {
        name
      }
    }
  }
`)

export const UPDATE_ESTABLISHMENT = graphql(`
  mutation UpdateEstablishment(
    $id: ID!
    $name: String
    $phone_number: String
    $alt_phone_number: String
    $address: String
    $collection_days: [CollectionDay!]
    $current_plan_id: ID
    $current_plan_period: Int
    $zone_id: ID
    $subscription_status: SubscriptionStatus
  ) {
    updateEstablishment(
      id: $id
      name: $name
      phone_number: $phone_number
      alt_phone_number: $alt_phone_number
      address: $address
      collection_days: $collection_days
      current_plan_id: $current_plan_id
      current_plan_period: $current_plan_period
      zone_id: $zone_id
      subscription_status: $subscription_status
    )
  }
`)
