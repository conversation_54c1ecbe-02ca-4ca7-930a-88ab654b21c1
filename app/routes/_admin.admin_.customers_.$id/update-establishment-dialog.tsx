import type { SelectedEstablishment, UpdateEstablishmentType } from './schema'
import type { Plan, Zone } from '~/gql/graphql'
import FormMessage from '~/components/common/form-message'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { CollectionDay, SubscriptionStatus } from '~/gql/graphql'
import { useAppForm } from '~/hooks/form'
import { updateEstablishmentSchema } from './schema'
import useUpdateEstablishment from './use-update-establishment'

interface Props {
  establishment: SelectedEstablishment
  isOpen: boolean
  toggle: (open: boolean) => void
  zones: Zone[]
  plans: Plan[]
}

export default function UpdateEstablishmentDialog({ establishment, isOpen, toggle, zones, plans }: Props) {
  const { updateEstablishment } = useUpdateEstablishment()

  const form = useAppForm({
    defaultValues: {
      id: establishment.id || '',
      name: establishment.name || '',
      phone_number: establishment.phone_number || '',
      address: establishment.address || '',
      alt_phone_number: establishment.alt_phone_number || '',
      current_plan_id: establishment.current_plan_id || '',
      current_plan_period: establishment.current_plan_period || 0,
      collection_days: establishment.collection_days?.map(d => d.toUpperCase()) || [],
      zone_id: establishment.zone_id || '',
      subscription_status: establishment.subscription_status.toUpperCase() || '',
    } as UpdateEstablishmentType,
    validators: {
      onSubmit: updateEstablishmentSchema,
    },
    onSubmit: async ({ value }) => {
      await updateEstablishment.mutateAsync(value, {
        onSuccess: () => {
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent className="w-full min-w-4xl">
        <DialogHeader>
          <DialogTitle>
            Update Establishment
          </DialogTitle>
          <DialogDescription>
            Enter updated establishment details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="grid grid-cols-2 gap-x-8 gap-y-4"
        >
          <div className="col-span-1">
            <form.AppField
              name="name"
              children={field => <field.InputField label="Name" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="address"
              children={field => <field.InputField label="Address" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="phone_number"
              children={field => <field.MobileInputField label="Phone number" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="alt_phone_number"
              children={field => <field.MobileInputField label="Alternate Phone number" />}
            />
          </div>

          <div className="col-span-2 my-4 border" />
          <div className="col-span-1">
            <form.AppField
              name="current_plan_period"
              children={field => <field.NumberField label="Billing Cycle (in months)" />}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="current_plan_id"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Subscription Plan</div>
                    <Select value={field.state.value} onValueChange={field.handleChange}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {plans.map((plan) => {
                          return <SelectItem key={plan.id} value={plan.id}>{plan.name}</SelectItem>
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="zone_id"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Zone</div>
                    <Select value={field.state.value} onValueChange={field.handleChange}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {zones.map((zone) => {
                          return <SelectItem key={zone.id} value={zone.id}>{zone.name}</SelectItem>
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="subscription_status"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Subscription Status</div>
                    <Select value={field.state.value} onValueChange={e => field.handleChange(e as SubscriptionStatus)}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={SubscriptionStatus.Active}>Active</SelectItem>
                        <SelectItem value={SubscriptionStatus.Inactive}>Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />

          </div>

          <div className="col-span-2 my-4 border" />

          <div className="col-span-2">
            <form.AppField
              name="collection_days"
              children={field => (
                <div className="flex flex-col gap-y-2">
                  <Label>Collection Days</Label>
                  <div className="grid grid-cols-7 gap-2">
                    <field.CheckboxInputField
                      label="Mon"
                      value={CollectionDay.Monday}
                    />
                    <field.CheckboxInputField
                      label="Tue"
                      value={CollectionDay.Tuesday}
                    />
                    <field.CheckboxInputField
                      label="Wed"
                      value={CollectionDay.Wednesday}
                    />
                    <field.CheckboxInputField
                      label="Thu"
                      value={CollectionDay.Thursday}
                    />
                    <field.CheckboxInputField
                      label="Fri"
                      value={CollectionDay.Friday}
                    />
                    <field.CheckboxInputField
                      label="Sat"
                      value={CollectionDay.Saturday}
                    />
                    <field.CheckboxInputField
                      label="Sun"
                      value={CollectionDay.Sunday}
                    />
                  </div>
                  <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                </div>
              )}
            />
          </div>
          <DialogFooter className="col-span-2 mt-2">
            <form.AppForm>
              <form.SubmitButton label="Update establishment" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>

    </Dialog>
  )
}
