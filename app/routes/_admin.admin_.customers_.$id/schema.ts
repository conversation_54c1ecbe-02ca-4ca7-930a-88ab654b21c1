import type { GetCustomerEstablishmentsQuery } from '~/gql/graphql'
import z from 'zod'
import { CollectionDay, SubscriptionStatus } from '~/gql/graphql'

export const establishmentSchema = z.object({
  name: z.string().optional(),
  phone_number: z.string().optional(),
  address: z.string().optional(),
  alt_phone_number: z.string().optional(),
  current_plan_id: z.string().optional(),
  collection_days: z.array(z.enum(CollectionDay)).optional(),
  zone_id: z.string().optional(),
  remarks: z.string().optional(),
})

export const updateEstablishmentSchema = z.object({
  id: z.string().optional(), // for update purposes
  name: z.string().optional(),
  address: z.string().optional(),
  phone_number: z.string().optional(),
  alt_phone_number: z.string().optional(),
  current_plan_id: z.string().optional(),
  current_plan_period: z.number().optional(),
  zone_id: z.string().optional(),
  collection_days: z.array(z.enum(CollectionDay)).optional(),
  subscription_status: z.enum(SubscriptionStatus).optional(),
})

export const subscriptionSchema = z.object({
  id: z.string().optional(),
  plan_id: z.string().optional(),
  period: z.number().optional(),
  remarks: z.string().optional(),
  start_date: z.string().optional(),
})

export const addSubscriptionSchema = z.object({
  customer_id: z.string(),
  establishment_input: establishmentSchema,
  subscription_input: subscriptionSchema,
})

export type SelectedEstablishment = NonNullable<GetCustomerEstablishmentsQuery['getCustomerEstablishment']>[number]
export type UpdateEstablishmentType = z.infer<typeof updateEstablishmentSchema>
export type AddSubscriptionType = z.infer<typeof addSubscriptionSchema>
