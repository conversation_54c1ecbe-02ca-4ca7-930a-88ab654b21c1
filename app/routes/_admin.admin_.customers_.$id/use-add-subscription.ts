import type { AddSubscriptionType } from './schema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { format } from 'date-fns'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { ADD_SUBSCRIPTION } from './graphql'

export default function useAddSubscription() {
  const queryClient = useQueryClient()

  const addSubscription = useMutation({
    mutationFn: async (data: AddSubscriptionType) => {
      const client = await graphqlClient()
      return await client.request({
        document: ADD_SUBSCRIPTION,
        variables: {
          ...data,
          subscription_input: {
            ...data.subscription_input,
            start_date: data.subscription_input.start_date ? format(new Date(data.subscription_input.start_date), 'yyyy-MM-dd hh:mm:ss') : undefined,
          },

        },
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['get-customers'],
      })
      queryClient.invalidateQueries({
        queryKey: ['customer-subscription-history'],
      })
      queryClient.invalidateQueries({
        queryKey: ['customer-establishments'],
      })
      toast.success('Subscription added')
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { addSubscription }
}
