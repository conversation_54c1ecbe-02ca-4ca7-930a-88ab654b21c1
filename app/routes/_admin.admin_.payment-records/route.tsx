import type { Route } from './+types/route'
import { useLoaderData } from 'react-router'
import PageHeader from '~/components/common/page-header'
import { GET_ZONES_AND_PLANS } from '~/graphql/queries/get-zones-and-plans'
import { graphqlClient } from '~/lib/graphql-client'
import { getSession } from '~/sessions'
import PaymentRecordQueries from './payment-record-queries'
import PaymentRecordsTable from './payment-records-table'

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get('Cookie'))
  const token = session.get('token')
  const client = await graphqlClient({ token })

  const data = await client.request({
    document: GET_ZONES_AND_PLANS,
  })

  const plans = data.getPlans || []

  return {
    plans,
  }
}

export default function PaymentRecords() {
  const { plans } = useLoaderData<typeof loader>()

  return (
    <div className="flex grow flex-col gap-4">
      <PageHeader title="Payment Records" />
      <PaymentRecordQueries plans={plans} />
      <PaymentRecordsTable />
    </div>

  )
}
