import { format } from 'date-fns'
import { ReceiptIcon } from 'lucide-react'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import MarkAsPaidDialog from '~/components/common/mark-as-paid-dialog'
import PagePagination from '~/components/common/page-pagination'
import ResendPaymentLinkDialog from '~/components/common/resend-payment-link-dialog'
import SendReceiptLinkDialog from '~/components/common/send-receipt-link-dialog'
import MarkAsPaidIcon from '~/components/icons/mark-as-paid-icon'
import MessageIcon from '~/components/icons/message-icon'
import { Button } from '~/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import useBoolean from '~/hooks/use-boolean'
import { capitalizeFirstLetter } from '~/lib/capitalize-first-letter'
import { formatDateRange } from '~/lib/format-date-range'
import { cn } from '~/lib/utils'
import useGetPaymentRecords from './use-get-payment-records'

export default function PaymentRecordsTable() {
  const { isOpen: openResend, toggle: toggleResend } = useBoolean()
  const { isOpen: openMarkAsPaid, toggle: toggleMarkAsPaid } = useBoolean()
  const { isOpen: openGenerateReceipt, toggle: toggleGenerateReceipt } = useBoolean()

  const [resend, setResend] = useState({
    customer_id: '',
    subscription_id: '',
    phone_number: '',
    payable_amount: 0,
  })

  const [generateReceipt, setGenerateReceipt] = useState({
    id: '',
    phone_number: '',
  })

  const [selectedId, setSelectedId] = useState('')

  const { data, isLoading, isError, handlePage, page, lastPage }
    = useGetPaymentRecords()

  const handleGenerateReceipt = (id: string, phone_number: string) => {
    setGenerateReceipt({
      id,
      phone_number,
    })
    toggleGenerateReceipt(true)
  }

  const handleMarkAsPaid = (id: string) => {
    setSelectedId(id)
    toggleMarkAsPaid(true)
  }

  const handleResend = (subscription_id: string, phone_number: string, payable_amount: number, customerId: string) => {
    setResend({
      customer_id: customerId,
      subscription_id,
      phone_number,
      payable_amount,
    })
    toggleResend(true)
  }

  return (
    <>
      <div
        className={`
          mt-4 flex w-full grow overflow-x-auto rounded-md bg-white p-2
        `}
      >
        <Table className="w-full min-w-[1000px] table-fixed">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Name</TableHead>
              <TableHead className="w-[120px]">Address</TableHead>
              <TableHead className="w-[80px]">Phone number</TableHead>
              <TableHead className="w-[50px]">Area</TableHead>
              <TableHead className="w-[100px]">Subscription Plan</TableHead>
              <TableHead className="w-[150px]">Month</TableHead>
              <TableHead className="w-[100px]">Payment Status</TableHead>
              <TableHead className="w-[100px]">Payment Date</TableHead>
              <TableHead className="w-[100px]">Payment Mode</TableHead>
              <TableHead className="w-[100px]">Delivery Status</TableHead>
              <TableHead className="w-[100px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading
              ? (
                  <TableRow>
                    <TableCell colSpan={11} className="py-8 text-center">
                      Loading...
                    </TableCell>
                  </TableRow>
                )
              : isError
                ? (
                    <TableRow>
                      <TableCell
                        colSpan={11}
                        className="py-8 text-center text-red-500"
                      >
                        Error loading data
                      </TableCell>
                    </TableRow>
                  )
                : data?.getPaymentRecords?.data?.length === 0
                  ? (
                      <TableRow>
                        <TableCell colSpan={11} className="py-8 text-center">
                          No records found
                        </TableCell>
                      </TableRow>
                    )
                  : data?.getPaymentRecords?.data?.map((record, parentIndex) => {
                      return record.subscriptionItems?.map((item, index) => {
                        return (
                          <TableRow
                            key={item.id}
                            className={cn('h-12 border-b border-black', index < ((record.subscriptionItems?.length || 0) - 1)
                              ? `border-0`
                              : '', parentIndex % 2 === 0
                              ? `bg-muted`
                              : '')}
                          >
                            <TableCell className="truncate">
                              {item.establishment?.name || '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {item.establishment?.address || '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {item.establishment?.phone_number || '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {item.establishment?.zone?.name || '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {item.plan?.name || '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {formatDateRange(record.start_date, record.end_date)}
                            </TableCell>
                            <TableCell className={cn('truncate text-green-700', {
                              'text-destructive': !record.paid_at,
                            })}
                            >
                              {record.paid_at ? 'Paid' : 'Unpaid'}
                            </TableCell>
                            <TableCell className="truncate">
                              {record?.paid_at ? format(new Date(record?.paid_at), 'do MMM yyyy') : '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {record.payment_mode ? capitalizeFirstLetter(record.payment_mode) : '-' }
                            </TableCell>
                            <TableCell className="truncate">
                              {record.latestWhatsApp?.message_status ? capitalizeFirstLetter(record.latestWhatsApp?.message_status) : '-' }
                            </TableCell>
                            <TableCell className="flex justify-end gap-x-4">
                              {index === 0 && (
                                <>
                                  <AppTooltip message="Generate receipt">
                                    <Button onClick={() => handleGenerateReceipt(record.id, record.customer.phone_number)} size="icon" variant="outline">
                                      <ReceiptIcon className="text-green-700" />
                                    </Button>
                                  </AppTooltip>
                                  <AppTooltip message="Mark as paid">
                                    <Button onClick={() => handleMarkAsPaid(record.id)} size="icon" variant="outline">
                                      <MarkAsPaidIcon className="text-green-700" />
                                    </Button>
                                  </AppTooltip>
                                  <AppTooltip message="Resend Payment Link">
                                    <Button
                                      size="icon"
                                      variant="outline"
                                      onClick={() => handleResend(record.id, record.customer.phone_number, record.payable_amount, record.customer.id)}
                                    >
                                      <MessageIcon className="text-green-700" />
                                    </Button>
                                  </AppTooltip>
                                </>
                              )}
                            </TableCell>
                          </TableRow>
                        )
                      })
                    })}
          </TableBody>
        </Table>
      </div>
      {lastPage > 1 && (
        <div className="flex justify-center">
          <PagePagination
            lastPage={lastPage}
            currentPage={page}
            handlePagePagination={handlePage}
          />
        </div>
      )}
      {generateReceipt.id && (
        <SendReceiptLinkDialog
          id={generateReceipt.id}
          phoneNumber={generateReceipt.phone_number}
          isOpen={openGenerateReceipt}
          toggle={toggleGenerateReceipt}
        />
      )}
      {resend.subscription_id && (
        <ResendPaymentLinkDialog
          customerId={resend.customer_id}
          phoneNumber={resend.phone_number}
          subscriptionId={resend.subscription_id}
          payableAmount={resend.payable_amount}
          isOpen={openResend}
          toggle={toggleResend}
        />
      )}
      {selectedId && (
        <MarkAsPaidDialog
          id={selectedId}
          isOpen={openMarkAsPaid}
          toggle={toggleMarkAsPaid}
        />
      )}
    </>
  )
}
