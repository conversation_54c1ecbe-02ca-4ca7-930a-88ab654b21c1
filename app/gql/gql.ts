/* eslint-disable */
import * as types from './graphql';
import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  mutation AdminLogin($username: String!, $password: String!) {\n    adminLogin(username: $username, password: $password) {\n      user {\n        id\n        username\n        role\n      }\n      exp\n      token\n    }\n  }\n": typeof types.AdminLoginDocument,
    "\n  mutation DeleteCustomer($id: ID!) {\n    deleteCustomer(id: $id)\n  }\n": typeof types.DeleteCustomerDocument,
    "\n  mutation Logout {\n    logout\n  }\n": typeof types.LogoutDocument,
    "\n  mutation UpdateCustomer(\n    $id: ID!,\n    $name: String,\n    $address: String, \n    $phone_number: String, \n    $alternate_phone_number: String,\n  ) {\n    updateCustomer(\n      id: $id\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n    )\n  }\n": typeof types.UpdateCustomerDocument,
    "\n  query CheckPaymentStatus($order_id: String!, $payment_id: String!) {\n    checkPaymentStatus(order_id: $order_id, payment_id: $payment_id) {\n      id\n      payment_receipt_link\n      payable_amount\n      paid_at\n    }\n  }\n": typeof types.CheckPaymentStatusDocument,
    "\n  query GetPlans {\n    getPlans {\n      id\n      name\n      price\n      remarks\n      is_internal\n      category\n    }\n  }\n": typeof types.GetPlansDocument,
    "\n  query GetZonesAndPlans {\n    getZones {\n      id\n      name\n    }\n    getPlans {\n      id\n      name\n      price\n      remarks\n      is_internal\n      category\n    }\n  }\n": typeof types.GetZonesAndPlansDocument,
    "\n  query GetZones {\n    getZones {\n      id\n      name\n    }\n  }\n": typeof types.GetZonesDocument,
    "\n  query GetCollectionScheduleNotificationStats($first: Int!, $page: Int) {\n    getCollectionScheduleNotificationStats(first: $first, page: $page) {\n      data {\n        date\n        total\n        week_day\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n": typeof types.GetCollectionScheduleNotificationStatsDocument,
    "\n  query GetCollectionSchedule($first: Int!, $page: Int, $collection_day: CollectionDay, $subscription_status: SubscriptionStatus) {\n    getCollectionSchedule(first: $first, page: $page, collection_day: $collection_day, subscription_status: $subscription_status) {\n      data {\n        id\n        name\n        address\n        phone_number\n        alternate_phone_number: alt_phone_number\n        plan {\n          name\n        }\n        collection_days\n        # zone {\n        #   id\n        #   name\n        # }\n        # currentPlan {\n        #   id\n        #   name\n        # }\n        # collection_day\n        # customer {\n        #   latestWhatsApp {\n        #     id\n        #     message_status\n        #   }\n        # }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n": typeof types.GetCollectionScheduleDocument,
    "\n  mutation SendCollectionScheduleNotification(\n    $establishment_ids: [ID!]\n    $remarks: String\n    $week_day: String!\n  ) {\n    sendCollectionScheduleNotification(establishment_ids: $establishment_ids, remarks: $remarks, week_day: $week_day)\n  }\n": typeof types.SendCollectionScheduleNotificationDocument,
    "\n  query SearchEstablishment(\n    # $first: Int!\n    # $name: String\n    # $customer_type: CustomerType\n    $keyword: String!\n  ) {\n    searchEstablishment(\n      keyword: $keyword\n      # first: $first\n      # name: $name\n      # customer_type: $customer_type\n    ) {\n        id\n        name\n        address\n        phone_number\n        zone {\n          id\n          name\n        }\n        collection_days\n        plan {\n          id\n          name\n        }\n        latestWhatsApp {\n          id\n          message_status\n        }\n  }\n}\n  ": typeof types.SearchEstablishmentDocument,
    "\n  query GetCollectionSentNotificationHistories(\n    $first: Int!\n    $page: Int\n    $week_day: String\n    $sent_date: DateTime\n  ) {\n    getCollectionSentNotificationHistories(first: $first, page: $page, week_day: $week_day, sent_date: $sent_date) {\n      data {\n        id\n        establishment {\n          id\n          name\n          address\n          phone_number\n          alternate_phone_number: alt_phone_number\n          plan {\n            id\n            name\n          }\n          collection_days\n          # zone {\n          #   id\n          #   name\n          # }\n          # currentPlan {\n          #   id\n          #   name\n          # }\n          # collection_day\n        }\n        week_day\n        latestWhatsApp {\n          id\n          message_status\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n": typeof types.GetCollectionSentNotificationHistoriesDocument,
    "\n  mutation RegisterCustomer(\n    $name: String!\n    $address: String!\n    $phone_number: String!\n    $alternate_phone_number: String\n    $plan_id: ID!\n  ) {\n    registerCustomer(\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n      plan_id: $plan_id\n    )\n  }\n": typeof types.RegisterCustomerDocument,
    "\n  mutation AddCustomer(\n    $name: String!\n    $address: String!\n    $phone_number: String!\n    $alternate_phone_number: String\n  ) {\n    addCustomer(\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n    )\n  }\n": typeof types.AddCustomerDocument,
    "\n  query getCustomers(\n    $first: Int!\n    $page: Int\n    $name: String\n    $subscription_status:SubscriptionStatus\n    $plan_id: ID\n    $start_date: DateTime\n    $end_date: DateTime\n    $zone_id: ID\n    $phone_number: String\n    $customer_type: CustomerType\n  ) {\n    getCustomers(\n      first: $first\n      page: $page\n      name: $name\n      subscription_status: $subscription_status\n      plan_id: $plan_id\n      zone_id: $zone_id\n      start_date: $start_date\n      end_date: $end_date\n      phone_number: $phone_number\n      customer_type: $customer_type\n    ) {\n      data {\n        id\n        name\n        address\n        phone_number\n        alternate_phone_number: alt_phone_number\n        created_at\n        verified_at\n        subscriptionHistory {\n          id\n        }\n        # remarks\n        # current_plan_period\n        # current_plan_id\n        # subscription_status\n        # latestSubscription {\n        #   id\n        #   # status\n        #   paid_at\n        #   start_date\n        #   remarks\n        #   payable_amount\n        #   plan {\n        #     id\n        #     name\n        #     price\n        #     remarks\n        #   }\n        # }\n        # zone {\n        #   id\n        #   name\n        # }\n        # collection_day\n        # currentPlan {\n        #   id\n        #   name\n        # }\n      }\n    paginator_info {\n      total\n      current_page\n      last_page\n    }\n  }\n}\n  ": typeof types.GetCustomersDocument,
    "\n  mutation GeneratePaymentReceiptLink(\n    $id: ID!\n    $phone_number: String\n    $should_send: Boolean!\n  ) {\n    generatePaymentReceiptLink(\n      id: $id\n      phone_number: $phone_number\n      should_send: $should_send\n    )\n  }\n": typeof types.GeneratePaymentReceiptLinkDocument,
    "\n  mutation ResendPaymentLink(\n    $customer_id: ID\n    $subscription_id: ID\n    $payable_amount: Float\n    $phone_number: String!\n  ) {\n    resendPaymentLink(\n      subscription_id: $subscription_id\n      phone_number: $phone_number\n      customer_id: $customer_id\n      payable_amount: $payable_amount\n    )\n  }\n": typeof types.ResendPaymentLinkDocument,
    "\n  mutation MarkAsPaid(\n    $subscription_id: ID!\n    $payment_mode: PaymentMode!\n    $remarks: String\n    $payment_date: DateTime\n  ) {\n    markAsPaid(\n      subscription_id: $subscription_id\n      payment_mode: $payment_mode\n      remarks: $remarks\n      payment_date: $payment_date\n    )\n  }\n": typeof types.MarkAsPaidDocument,
    "\n  mutation DeleteEstablishment(\n    $id: ID!\n  ) {\n    deleteEstablishment(id: $id)\n  }\n": typeof types.DeleteEstablishmentDocument,
    "\n  mutation AddSubscription(\n    $customer_id: ID!\n    $establishment_input: EstablishmentInput!\n    $subscription_input: SubscriptionInput!\n  ) {\n    addSubscription(\n      customer_id: $customer_id\n      establishment_input: $establishment_input\n      subscription_input: $subscription_input\n    )\n  }\n": typeof types.AddSubscriptionDocument,
    "\n  query GetCustomerSubscriptionHistory($customer_id: ID!, $first: Int!, $page: Int) {\n    getCustomerSubscriptionHistory(customer_id: $customer_id, first: $first, page: $page) {\n      data {\n        id\n        start_date\n        end_date\n        payable_amount\n        customer {\n          id\n          name\n          address\n          phone_number\n        }\n        payment_mode\n        paymentOrder {\n          id\n          paymentable_type\n          paymentable {\n            ... on RzpayPaymentOrder{\n              id\n              payment_status\n              payment_error\n            }\n          }\n        }\n        latestWhatsApp{\n          id\n          message_status\n        }\n        paid_at\n        remarks\n        subscriptionItems {\n          id\n          plan {\n            id\n            name\n            price\n          }\n          establishment {\n            id\n            name\n            address\n            phone_number\n          }\n          period\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n": typeof types.GetCustomerSubscriptionHistoryDocument,
    "\n  query GetCustomerEstablishments($customer_id: ID!) {\n    getCustomerEstablishment(customer_id: $customer_id) {\n      id\n      name\n      phone_number\n      alt_phone_number\n      address\n      remarks\n      collection_days\n      zone_id\n      current_plan_id\n      current_plan_period\n      subscription_status\n      plan {\n        name\n      }\n    }\n  }\n": typeof types.GetCustomerEstablishmentsDocument,
    "\n  mutation UpdateEstablishment(\n    $id: ID!\n    $name: String\n    $phone_number: String\n    $alt_phone_number: String\n    $address: String\n    $collection_days: [CollectionDay!]\n    $current_plan_id: ID\n    $current_plan_period: Int\n    $zone_id: ID\n    $subscription_status: SubscriptionStatus\n  ) {\n    updateEstablishment(\n      id: $id\n      name: $name\n      phone_number: $phone_number\n      alt_phone_number: $alt_phone_number\n      address: $address\n      collection_days: $collection_days\n      current_plan_id: $current_plan_id\n      current_plan_period: $current_plan_period\n      zone_id: $zone_id\n      subscription_status: $subscription_status\n    )\n  }\n": typeof types.UpdateEstablishmentDocument,
    "\n  mutation UpdateProduct(\n    $id: ID!\n    $item_name: String\n    $stock: Int\n    $price: Float\n    $discount: Float\n  ) {\n    updateProduct(id: $id, item_name: $item_name, stock: $stock, price: $price, discount: $discount)\n  }\n": typeof types.UpdateProductDocument,
    "\n  query GetProducts {\n    getProducts {\n      id\n      item_name\n      stock\n      price\n      discount\n    }\n  }\n": typeof types.GetProductsDocument,
    "\n  query SearchCustomerByName($name: String!) {\n    getCustomers(first: 20, name: $name) {\n      data {\n        id\n        name\n      }\n    }\n  }\n": typeof types.SearchCustomerByNameDocument,
    "\n  mutation AddSale(\n    $customer_id: ID!\n    $payment_mode: PaymentMode!\n    $products: [ProductInput!]!\n  ) {\n    addSale(\n      customer_id: $customer_id\n      payment_mode: $payment_mode\n      products: $products\n    )\n  }\n": typeof types.AddSaleDocument,
    "\n  mutation DeleteSale(\n    $id: ID!\n  ) {\n    deleteSale(id: $id) {\n      id\n    }\n  }\n": typeof types.DeleteSaleDocument,
    "\n  query GetSales(\n    $first: Int!\n    $page: Int\n    $product_name: String\n    $start_date: DateTime\n    $end_date: DateTime\n  ) {\n    getSales(\n      first: $first\n      page: $page\n      product_name: $product_name\n      start_date: $start_date\n      end_date: $end_date\n    ) {\n      data {\n        id\n        payment_mode\n        customer {\n          id\n          name\n          address\n          phone_number\n        }\n        productSales {\n          id\n          quantity\n          price_per_unit\n          discount\n          product {\n            id\n            item_name\n          }\n        }\n        created_at\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n": typeof types.GetSalesDocument,
    "\n  query GetPaymentRecords(\n    $first: Int!\n    $month: Int!\n    $year: Int!\n    $page: Int\n    $keyword: String\n    $plan_id: ID\n    $is_paid: Boolean\n  ) {\n    getPaymentRecords(\n      first: $first\n      page: $page\n      month: $month\n      year: $year\n      keyword: $keyword\n      plan_id: $plan_id\n      is_paid: $is_paid\n    ) {\n      data {\n        id\n        # overdue\n        start_date\n        end_date\n        payable_amount\n        customer {\n          id\n          name\n          phone_number\n          address\n        }\n        subscriptionItems {\n          id\n          plan {\n            id\n            name\n          }\n          establishment {\n            id\n            name\n            address\n            phone_number\n            zone {\n              id\n              name\n            }\n          }\n        }\n        paid_at\n        payment_mode\n        latestWhatsApp {\n          id\n          message_status\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n": typeof types.GetPaymentRecordsDocument,
    "\n  mutation AddPlan(\n    $name: String!\n    $price: Float!\n    $remarks: String\n    $is_internal: Boolean!\n    $category: PlanCategory!\n  ) {\n    addPlan(name: $name, price: $price, remarks: $remarks, is_internal: $is_internal, category: $category)\n  }\n": typeof types.AddPlanDocument,
    "\n  mutation UpdatePlan(\n    $id: ID!\n    $name: String\n    $price: Float\n    $remarks: String\n    $is_internal: Boolean\n    $category: PlanCategory\n  ) {\n    updatePlan(id: $id, name: $name, price: $price, remarks: $remarks, is_internal: $is_internal, category: $category)\n  }\n": typeof types.UpdatePlanDocument,
    "\n  mutation DeletePlan(\n    $id: ID!\n  ) {\n    deletePlan(id: $id)\n  }\n": typeof types.DeletePlanDocument,
    "\n  query SubscriberPlanDistribution($zone_id: ID) {\n    subscriberPlanDistribution(zone_id: $zone_id) {\n      plan_name\n      total_subscriber\n    }\n  }\n": typeof types.SubscriberPlanDistributionDocument,
    "\n  query SubscriberGrowthTrend($year: Int!) {\n    subscriberGrowthTrend(year: $year) {\n      month\n      total_subscriber\n    }\n  }\n": typeof types.SubscriberGrowthTrendDocument,
    "\n  query SubscriptionPaymentTrend($month: Int!, $year: Int!, $plan_id: ID, $zone_id: ID) {\n    subscriptionPaymentTrend(month: $month, year: $year, plan_id: $plan_id, zone_id: $zone_id) {\n      day\n      total_amount\n    }\n  }\n": typeof types.SubscriptionPaymentTrendDocument,
    "\n  query GetBasicStats($start_date: DateTime, $end_date: DateTime) {\n    getBasicStats(start_date: $start_date, end_date: $end_date) {\n      total_subscriber\n      total_subscription_fee\n      total_fee_paid\n      total_fee_unpaid\n    }\n  }\n": typeof types.GetBasicStatsDocument,
    "\n  mutation ProcessSubscriptionPaymentLink(\n    $payment_link_hash: String!\n  ) {\n    processSubscriptionPaymentLink(\n      payment_link_hash: $payment_link_hash\n    ) {\n      ... on RzpayPaymentOrder {\n        id\n        order_id\n        payment_error\n        payment_id\n        payment_instrument_type\n        payment_status\n        refund_amount\n        refund_error\n        refund_status\n      }\n    }\n  }\n": typeof types.ProcessSubscriptionPaymentLinkDocument,
};
const documents: Documents = {
    "\n  mutation AdminLogin($username: String!, $password: String!) {\n    adminLogin(username: $username, password: $password) {\n      user {\n        id\n        username\n        role\n      }\n      exp\n      token\n    }\n  }\n": types.AdminLoginDocument,
    "\n  mutation DeleteCustomer($id: ID!) {\n    deleteCustomer(id: $id)\n  }\n": types.DeleteCustomerDocument,
    "\n  mutation Logout {\n    logout\n  }\n": types.LogoutDocument,
    "\n  mutation UpdateCustomer(\n    $id: ID!,\n    $name: String,\n    $address: String, \n    $phone_number: String, \n    $alternate_phone_number: String,\n  ) {\n    updateCustomer(\n      id: $id\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n    )\n  }\n": types.UpdateCustomerDocument,
    "\n  query CheckPaymentStatus($order_id: String!, $payment_id: String!) {\n    checkPaymentStatus(order_id: $order_id, payment_id: $payment_id) {\n      id\n      payment_receipt_link\n      payable_amount\n      paid_at\n    }\n  }\n": types.CheckPaymentStatusDocument,
    "\n  query GetPlans {\n    getPlans {\n      id\n      name\n      price\n      remarks\n      is_internal\n      category\n    }\n  }\n": types.GetPlansDocument,
    "\n  query GetZonesAndPlans {\n    getZones {\n      id\n      name\n    }\n    getPlans {\n      id\n      name\n      price\n      remarks\n      is_internal\n      category\n    }\n  }\n": types.GetZonesAndPlansDocument,
    "\n  query GetZones {\n    getZones {\n      id\n      name\n    }\n  }\n": types.GetZonesDocument,
    "\n  query GetCollectionScheduleNotificationStats($first: Int!, $page: Int) {\n    getCollectionScheduleNotificationStats(first: $first, page: $page) {\n      data {\n        date\n        total\n        week_day\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n": types.GetCollectionScheduleNotificationStatsDocument,
    "\n  query GetCollectionSchedule($first: Int!, $page: Int, $collection_day: CollectionDay, $subscription_status: SubscriptionStatus) {\n    getCollectionSchedule(first: $first, page: $page, collection_day: $collection_day, subscription_status: $subscription_status) {\n      data {\n        id\n        name\n        address\n        phone_number\n        alternate_phone_number: alt_phone_number\n        plan {\n          name\n        }\n        collection_days\n        # zone {\n        #   id\n        #   name\n        # }\n        # currentPlan {\n        #   id\n        #   name\n        # }\n        # collection_day\n        # customer {\n        #   latestWhatsApp {\n        #     id\n        #     message_status\n        #   }\n        # }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n": types.GetCollectionScheduleDocument,
    "\n  mutation SendCollectionScheduleNotification(\n    $establishment_ids: [ID!]\n    $remarks: String\n    $week_day: String!\n  ) {\n    sendCollectionScheduleNotification(establishment_ids: $establishment_ids, remarks: $remarks, week_day: $week_day)\n  }\n": types.SendCollectionScheduleNotificationDocument,
    "\n  query SearchEstablishment(\n    # $first: Int!\n    # $name: String\n    # $customer_type: CustomerType\n    $keyword: String!\n  ) {\n    searchEstablishment(\n      keyword: $keyword\n      # first: $first\n      # name: $name\n      # customer_type: $customer_type\n    ) {\n        id\n        name\n        address\n        phone_number\n        zone {\n          id\n          name\n        }\n        collection_days\n        plan {\n          id\n          name\n        }\n        latestWhatsApp {\n          id\n          message_status\n        }\n  }\n}\n  ": types.SearchEstablishmentDocument,
    "\n  query GetCollectionSentNotificationHistories(\n    $first: Int!\n    $page: Int\n    $week_day: String\n    $sent_date: DateTime\n  ) {\n    getCollectionSentNotificationHistories(first: $first, page: $page, week_day: $week_day, sent_date: $sent_date) {\n      data {\n        id\n        establishment {\n          id\n          name\n          address\n          phone_number\n          alternate_phone_number: alt_phone_number\n          plan {\n            id\n            name\n          }\n          collection_days\n          # zone {\n          #   id\n          #   name\n          # }\n          # currentPlan {\n          #   id\n          #   name\n          # }\n          # collection_day\n        }\n        week_day\n        latestWhatsApp {\n          id\n          message_status\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n": types.GetCollectionSentNotificationHistoriesDocument,
    "\n  mutation RegisterCustomer(\n    $name: String!\n    $address: String!\n    $phone_number: String!\n    $alternate_phone_number: String\n    $plan_id: ID!\n  ) {\n    registerCustomer(\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n      plan_id: $plan_id\n    )\n  }\n": types.RegisterCustomerDocument,
    "\n  mutation AddCustomer(\n    $name: String!\n    $address: String!\n    $phone_number: String!\n    $alternate_phone_number: String\n  ) {\n    addCustomer(\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n    )\n  }\n": types.AddCustomerDocument,
    "\n  query getCustomers(\n    $first: Int!\n    $page: Int\n    $name: String\n    $subscription_status:SubscriptionStatus\n    $plan_id: ID\n    $start_date: DateTime\n    $end_date: DateTime\n    $zone_id: ID\n    $phone_number: String\n    $customer_type: CustomerType\n  ) {\n    getCustomers(\n      first: $first\n      page: $page\n      name: $name\n      subscription_status: $subscription_status\n      plan_id: $plan_id\n      zone_id: $zone_id\n      start_date: $start_date\n      end_date: $end_date\n      phone_number: $phone_number\n      customer_type: $customer_type\n    ) {\n      data {\n        id\n        name\n        address\n        phone_number\n        alternate_phone_number: alt_phone_number\n        created_at\n        verified_at\n        subscriptionHistory {\n          id\n        }\n        # remarks\n        # current_plan_period\n        # current_plan_id\n        # subscription_status\n        # latestSubscription {\n        #   id\n        #   # status\n        #   paid_at\n        #   start_date\n        #   remarks\n        #   payable_amount\n        #   plan {\n        #     id\n        #     name\n        #     price\n        #     remarks\n        #   }\n        # }\n        # zone {\n        #   id\n        #   name\n        # }\n        # collection_day\n        # currentPlan {\n        #   id\n        #   name\n        # }\n      }\n    paginator_info {\n      total\n      current_page\n      last_page\n    }\n  }\n}\n  ": types.GetCustomersDocument,
    "\n  mutation GeneratePaymentReceiptLink(\n    $id: ID!\n    $phone_number: String\n    $should_send: Boolean!\n  ) {\n    generatePaymentReceiptLink(\n      id: $id\n      phone_number: $phone_number\n      should_send: $should_send\n    )\n  }\n": types.GeneratePaymentReceiptLinkDocument,
    "\n  mutation ResendPaymentLink(\n    $customer_id: ID\n    $subscription_id: ID\n    $payable_amount: Float\n    $phone_number: String!\n  ) {\n    resendPaymentLink(\n      subscription_id: $subscription_id\n      phone_number: $phone_number\n      customer_id: $customer_id\n      payable_amount: $payable_amount\n    )\n  }\n": types.ResendPaymentLinkDocument,
    "\n  mutation MarkAsPaid(\n    $subscription_id: ID!\n    $payment_mode: PaymentMode!\n    $remarks: String\n    $payment_date: DateTime\n  ) {\n    markAsPaid(\n      subscription_id: $subscription_id\n      payment_mode: $payment_mode\n      remarks: $remarks\n      payment_date: $payment_date\n    )\n  }\n": types.MarkAsPaidDocument,
    "\n  mutation DeleteEstablishment(\n    $id: ID!\n  ) {\n    deleteEstablishment(id: $id)\n  }\n": types.DeleteEstablishmentDocument,
    "\n  mutation AddSubscription(\n    $customer_id: ID!\n    $establishment_input: EstablishmentInput!\n    $subscription_input: SubscriptionInput!\n  ) {\n    addSubscription(\n      customer_id: $customer_id\n      establishment_input: $establishment_input\n      subscription_input: $subscription_input\n    )\n  }\n": types.AddSubscriptionDocument,
    "\n  query GetCustomerSubscriptionHistory($customer_id: ID!, $first: Int!, $page: Int) {\n    getCustomerSubscriptionHistory(customer_id: $customer_id, first: $first, page: $page) {\n      data {\n        id\n        start_date\n        end_date\n        payable_amount\n        customer {\n          id\n          name\n          address\n          phone_number\n        }\n        payment_mode\n        paymentOrder {\n          id\n          paymentable_type\n          paymentable {\n            ... on RzpayPaymentOrder{\n              id\n              payment_status\n              payment_error\n            }\n          }\n        }\n        latestWhatsApp{\n          id\n          message_status\n        }\n        paid_at\n        remarks\n        subscriptionItems {\n          id\n          plan {\n            id\n            name\n            price\n          }\n          establishment {\n            id\n            name\n            address\n            phone_number\n          }\n          period\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n": types.GetCustomerSubscriptionHistoryDocument,
    "\n  query GetCustomerEstablishments($customer_id: ID!) {\n    getCustomerEstablishment(customer_id: $customer_id) {\n      id\n      name\n      phone_number\n      alt_phone_number\n      address\n      remarks\n      collection_days\n      zone_id\n      current_plan_id\n      current_plan_period\n      subscription_status\n      plan {\n        name\n      }\n    }\n  }\n": types.GetCustomerEstablishmentsDocument,
    "\n  mutation UpdateEstablishment(\n    $id: ID!\n    $name: String\n    $phone_number: String\n    $alt_phone_number: String\n    $address: String\n    $collection_days: [CollectionDay!]\n    $current_plan_id: ID\n    $current_plan_period: Int\n    $zone_id: ID\n    $subscription_status: SubscriptionStatus\n  ) {\n    updateEstablishment(\n      id: $id\n      name: $name\n      phone_number: $phone_number\n      alt_phone_number: $alt_phone_number\n      address: $address\n      collection_days: $collection_days\n      current_plan_id: $current_plan_id\n      current_plan_period: $current_plan_period\n      zone_id: $zone_id\n      subscription_status: $subscription_status\n    )\n  }\n": types.UpdateEstablishmentDocument,
    "\n  mutation UpdateProduct(\n    $id: ID!\n    $item_name: String\n    $stock: Int\n    $price: Float\n    $discount: Float\n  ) {\n    updateProduct(id: $id, item_name: $item_name, stock: $stock, price: $price, discount: $discount)\n  }\n": types.UpdateProductDocument,
    "\n  query GetProducts {\n    getProducts {\n      id\n      item_name\n      stock\n      price\n      discount\n    }\n  }\n": types.GetProductsDocument,
    "\n  query SearchCustomerByName($name: String!) {\n    getCustomers(first: 20, name: $name) {\n      data {\n        id\n        name\n      }\n    }\n  }\n": types.SearchCustomerByNameDocument,
    "\n  mutation AddSale(\n    $customer_id: ID!\n    $payment_mode: PaymentMode!\n    $products: [ProductInput!]!\n  ) {\n    addSale(\n      customer_id: $customer_id\n      payment_mode: $payment_mode\n      products: $products\n    )\n  }\n": types.AddSaleDocument,
    "\n  mutation DeleteSale(\n    $id: ID!\n  ) {\n    deleteSale(id: $id) {\n      id\n    }\n  }\n": types.DeleteSaleDocument,
    "\n  query GetSales(\n    $first: Int!\n    $page: Int\n    $product_name: String\n    $start_date: DateTime\n    $end_date: DateTime\n  ) {\n    getSales(\n      first: $first\n      page: $page\n      product_name: $product_name\n      start_date: $start_date\n      end_date: $end_date\n    ) {\n      data {\n        id\n        payment_mode\n        customer {\n          id\n          name\n          address\n          phone_number\n        }\n        productSales {\n          id\n          quantity\n          price_per_unit\n          discount\n          product {\n            id\n            item_name\n          }\n        }\n        created_at\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n": types.GetSalesDocument,
    "\n  query GetPaymentRecords(\n    $first: Int!\n    $month: Int!\n    $year: Int!\n    $page: Int\n    $keyword: String\n    $plan_id: ID\n    $is_paid: Boolean\n  ) {\n    getPaymentRecords(\n      first: $first\n      page: $page\n      month: $month\n      year: $year\n      keyword: $keyword\n      plan_id: $plan_id\n      is_paid: $is_paid\n    ) {\n      data {\n        id\n        # overdue\n        start_date\n        end_date\n        payable_amount\n        customer {\n          id\n          name\n          phone_number\n          address\n        }\n        subscriptionItems {\n          id\n          plan {\n            id\n            name\n          }\n          establishment {\n            id\n            name\n            address\n            phone_number\n            zone {\n              id\n              name\n            }\n          }\n        }\n        paid_at\n        payment_mode\n        latestWhatsApp {\n          id\n          message_status\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n": types.GetPaymentRecordsDocument,
    "\n  mutation AddPlan(\n    $name: String!\n    $price: Float!\n    $remarks: String\n    $is_internal: Boolean!\n    $category: PlanCategory!\n  ) {\n    addPlan(name: $name, price: $price, remarks: $remarks, is_internal: $is_internal, category: $category)\n  }\n": types.AddPlanDocument,
    "\n  mutation UpdatePlan(\n    $id: ID!\n    $name: String\n    $price: Float\n    $remarks: String\n    $is_internal: Boolean\n    $category: PlanCategory\n  ) {\n    updatePlan(id: $id, name: $name, price: $price, remarks: $remarks, is_internal: $is_internal, category: $category)\n  }\n": types.UpdatePlanDocument,
    "\n  mutation DeletePlan(\n    $id: ID!\n  ) {\n    deletePlan(id: $id)\n  }\n": types.DeletePlanDocument,
    "\n  query SubscriberPlanDistribution($zone_id: ID) {\n    subscriberPlanDistribution(zone_id: $zone_id) {\n      plan_name\n      total_subscriber\n    }\n  }\n": types.SubscriberPlanDistributionDocument,
    "\n  query SubscriberGrowthTrend($year: Int!) {\n    subscriberGrowthTrend(year: $year) {\n      month\n      total_subscriber\n    }\n  }\n": types.SubscriberGrowthTrendDocument,
    "\n  query SubscriptionPaymentTrend($month: Int!, $year: Int!, $plan_id: ID, $zone_id: ID) {\n    subscriptionPaymentTrend(month: $month, year: $year, plan_id: $plan_id, zone_id: $zone_id) {\n      day\n      total_amount\n    }\n  }\n": types.SubscriptionPaymentTrendDocument,
    "\n  query GetBasicStats($start_date: DateTime, $end_date: DateTime) {\n    getBasicStats(start_date: $start_date, end_date: $end_date) {\n      total_subscriber\n      total_subscription_fee\n      total_fee_paid\n      total_fee_unpaid\n    }\n  }\n": types.GetBasicStatsDocument,
    "\n  mutation ProcessSubscriptionPaymentLink(\n    $payment_link_hash: String!\n  ) {\n    processSubscriptionPaymentLink(\n      payment_link_hash: $payment_link_hash\n    ) {\n      ... on RzpayPaymentOrder {\n        id\n        order_id\n        payment_error\n        payment_id\n        payment_instrument_type\n        payment_status\n        refund_amount\n        refund_error\n        refund_status\n      }\n    }\n  }\n": types.ProcessSubscriptionPaymentLinkDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AdminLogin($username: String!, $password: String!) {\n    adminLogin(username: $username, password: $password) {\n      user {\n        id\n        username\n        role\n      }\n      exp\n      token\n    }\n  }\n"): (typeof documents)["\n  mutation AdminLogin($username: String!, $password: String!) {\n    adminLogin(username: $username, password: $password) {\n      user {\n        id\n        username\n        role\n      }\n      exp\n      token\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteCustomer($id: ID!) {\n    deleteCustomer(id: $id)\n  }\n"): (typeof documents)["\n  mutation DeleteCustomer($id: ID!) {\n    deleteCustomer(id: $id)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Logout {\n    logout\n  }\n"): (typeof documents)["\n  mutation Logout {\n    logout\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateCustomer(\n    $id: ID!,\n    $name: String,\n    $address: String, \n    $phone_number: String, \n    $alternate_phone_number: String,\n  ) {\n    updateCustomer(\n      id: $id\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n    )\n  }\n"): (typeof documents)["\n  mutation UpdateCustomer(\n    $id: ID!,\n    $name: String,\n    $address: String, \n    $phone_number: String, \n    $alternate_phone_number: String,\n  ) {\n    updateCustomer(\n      id: $id\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query CheckPaymentStatus($order_id: String!, $payment_id: String!) {\n    checkPaymentStatus(order_id: $order_id, payment_id: $payment_id) {\n      id\n      payment_receipt_link\n      payable_amount\n      paid_at\n    }\n  }\n"): (typeof documents)["\n  query CheckPaymentStatus($order_id: String!, $payment_id: String!) {\n    checkPaymentStatus(order_id: $order_id, payment_id: $payment_id) {\n      id\n      payment_receipt_link\n      payable_amount\n      paid_at\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetPlans {\n    getPlans {\n      id\n      name\n      price\n      remarks\n      is_internal\n      category\n    }\n  }\n"): (typeof documents)["\n  query GetPlans {\n    getPlans {\n      id\n      name\n      price\n      remarks\n      is_internal\n      category\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetZonesAndPlans {\n    getZones {\n      id\n      name\n    }\n    getPlans {\n      id\n      name\n      price\n      remarks\n      is_internal\n      category\n    }\n  }\n"): (typeof documents)["\n  query GetZonesAndPlans {\n    getZones {\n      id\n      name\n    }\n    getPlans {\n      id\n      name\n      price\n      remarks\n      is_internal\n      category\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetZones {\n    getZones {\n      id\n      name\n    }\n  }\n"): (typeof documents)["\n  query GetZones {\n    getZones {\n      id\n      name\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCollectionScheduleNotificationStats($first: Int!, $page: Int) {\n    getCollectionScheduleNotificationStats(first: $first, page: $page) {\n      data {\n        date\n        total\n        week_day\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCollectionScheduleNotificationStats($first: Int!, $page: Int) {\n    getCollectionScheduleNotificationStats(first: $first, page: $page) {\n      data {\n        date\n        total\n        week_day\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCollectionSchedule($first: Int!, $page: Int, $collection_day: CollectionDay, $subscription_status: SubscriptionStatus) {\n    getCollectionSchedule(first: $first, page: $page, collection_day: $collection_day, subscription_status: $subscription_status) {\n      data {\n        id\n        name\n        address\n        phone_number\n        alternate_phone_number: alt_phone_number\n        plan {\n          name\n        }\n        collection_days\n        # zone {\n        #   id\n        #   name\n        # }\n        # currentPlan {\n        #   id\n        #   name\n        # }\n        # collection_day\n        # customer {\n        #   latestWhatsApp {\n        #     id\n        #     message_status\n        #   }\n        # }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCollectionSchedule($first: Int!, $page: Int, $collection_day: CollectionDay, $subscription_status: SubscriptionStatus) {\n    getCollectionSchedule(first: $first, page: $page, collection_day: $collection_day, subscription_status: $subscription_status) {\n      data {\n        id\n        name\n        address\n        phone_number\n        alternate_phone_number: alt_phone_number\n        plan {\n          name\n        }\n        collection_days\n        # zone {\n        #   id\n        #   name\n        # }\n        # currentPlan {\n        #   id\n        #   name\n        # }\n        # collection_day\n        # customer {\n        #   latestWhatsApp {\n        #     id\n        #     message_status\n        #   }\n        # }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation SendCollectionScheduleNotification(\n    $establishment_ids: [ID!]\n    $remarks: String\n    $week_day: String!\n  ) {\n    sendCollectionScheduleNotification(establishment_ids: $establishment_ids, remarks: $remarks, week_day: $week_day)\n  }\n"): (typeof documents)["\n  mutation SendCollectionScheduleNotification(\n    $establishment_ids: [ID!]\n    $remarks: String\n    $week_day: String!\n  ) {\n    sendCollectionScheduleNotification(establishment_ids: $establishment_ids, remarks: $remarks, week_day: $week_day)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SearchEstablishment(\n    # $first: Int!\n    # $name: String\n    # $customer_type: CustomerType\n    $keyword: String!\n  ) {\n    searchEstablishment(\n      keyword: $keyword\n      # first: $first\n      # name: $name\n      # customer_type: $customer_type\n    ) {\n        id\n        name\n        address\n        phone_number\n        zone {\n          id\n          name\n        }\n        collection_days\n        plan {\n          id\n          name\n        }\n        latestWhatsApp {\n          id\n          message_status\n        }\n  }\n}\n  "): (typeof documents)["\n  query SearchEstablishment(\n    # $first: Int!\n    # $name: String\n    # $customer_type: CustomerType\n    $keyword: String!\n  ) {\n    searchEstablishment(\n      keyword: $keyword\n      # first: $first\n      # name: $name\n      # customer_type: $customer_type\n    ) {\n        id\n        name\n        address\n        phone_number\n        zone {\n          id\n          name\n        }\n        collection_days\n        plan {\n          id\n          name\n        }\n        latestWhatsApp {\n          id\n          message_status\n        }\n  }\n}\n  "];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCollectionSentNotificationHistories(\n    $first: Int!\n    $page: Int\n    $week_day: String\n    $sent_date: DateTime\n  ) {\n    getCollectionSentNotificationHistories(first: $first, page: $page, week_day: $week_day, sent_date: $sent_date) {\n      data {\n        id\n        establishment {\n          id\n          name\n          address\n          phone_number\n          alternate_phone_number: alt_phone_number\n          plan {\n            id\n            name\n          }\n          collection_days\n          # zone {\n          #   id\n          #   name\n          # }\n          # currentPlan {\n          #   id\n          #   name\n          # }\n          # collection_day\n        }\n        week_day\n        latestWhatsApp {\n          id\n          message_status\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCollectionSentNotificationHistories(\n    $first: Int!\n    $page: Int\n    $week_day: String\n    $sent_date: DateTime\n  ) {\n    getCollectionSentNotificationHistories(first: $first, page: $page, week_day: $week_day, sent_date: $sent_date) {\n      data {\n        id\n        establishment {\n          id\n          name\n          address\n          phone_number\n          alternate_phone_number: alt_phone_number\n          plan {\n            id\n            name\n          }\n          collection_days\n          # zone {\n          #   id\n          #   name\n          # }\n          # currentPlan {\n          #   id\n          #   name\n          # }\n          # collection_day\n        }\n        week_day\n        latestWhatsApp {\n          id\n          message_status\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation RegisterCustomer(\n    $name: String!\n    $address: String!\n    $phone_number: String!\n    $alternate_phone_number: String\n    $plan_id: ID!\n  ) {\n    registerCustomer(\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n      plan_id: $plan_id\n    )\n  }\n"): (typeof documents)["\n  mutation RegisterCustomer(\n    $name: String!\n    $address: String!\n    $phone_number: String!\n    $alternate_phone_number: String\n    $plan_id: ID!\n  ) {\n    registerCustomer(\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n      plan_id: $plan_id\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddCustomer(\n    $name: String!\n    $address: String!\n    $phone_number: String!\n    $alternate_phone_number: String\n  ) {\n    addCustomer(\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n    )\n  }\n"): (typeof documents)["\n  mutation AddCustomer(\n    $name: String!\n    $address: String!\n    $phone_number: String!\n    $alternate_phone_number: String\n  ) {\n    addCustomer(\n      name: $name\n      address: $address\n      phone_number: $phone_number\n      alternate_phone_number: $alternate_phone_number\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getCustomers(\n    $first: Int!\n    $page: Int\n    $name: String\n    $subscription_status:SubscriptionStatus\n    $plan_id: ID\n    $start_date: DateTime\n    $end_date: DateTime\n    $zone_id: ID\n    $phone_number: String\n    $customer_type: CustomerType\n  ) {\n    getCustomers(\n      first: $first\n      page: $page\n      name: $name\n      subscription_status: $subscription_status\n      plan_id: $plan_id\n      zone_id: $zone_id\n      start_date: $start_date\n      end_date: $end_date\n      phone_number: $phone_number\n      customer_type: $customer_type\n    ) {\n      data {\n        id\n        name\n        address\n        phone_number\n        alternate_phone_number: alt_phone_number\n        created_at\n        verified_at\n        subscriptionHistory {\n          id\n        }\n        # remarks\n        # current_plan_period\n        # current_plan_id\n        # subscription_status\n        # latestSubscription {\n        #   id\n        #   # status\n        #   paid_at\n        #   start_date\n        #   remarks\n        #   payable_amount\n        #   plan {\n        #     id\n        #     name\n        #     price\n        #     remarks\n        #   }\n        # }\n        # zone {\n        #   id\n        #   name\n        # }\n        # collection_day\n        # currentPlan {\n        #   id\n        #   name\n        # }\n      }\n    paginator_info {\n      total\n      current_page\n      last_page\n    }\n  }\n}\n  "): (typeof documents)["\n  query getCustomers(\n    $first: Int!\n    $page: Int\n    $name: String\n    $subscription_status:SubscriptionStatus\n    $plan_id: ID\n    $start_date: DateTime\n    $end_date: DateTime\n    $zone_id: ID\n    $phone_number: String\n    $customer_type: CustomerType\n  ) {\n    getCustomers(\n      first: $first\n      page: $page\n      name: $name\n      subscription_status: $subscription_status\n      plan_id: $plan_id\n      zone_id: $zone_id\n      start_date: $start_date\n      end_date: $end_date\n      phone_number: $phone_number\n      customer_type: $customer_type\n    ) {\n      data {\n        id\n        name\n        address\n        phone_number\n        alternate_phone_number: alt_phone_number\n        created_at\n        verified_at\n        subscriptionHistory {\n          id\n        }\n        # remarks\n        # current_plan_period\n        # current_plan_id\n        # subscription_status\n        # latestSubscription {\n        #   id\n        #   # status\n        #   paid_at\n        #   start_date\n        #   remarks\n        #   payable_amount\n        #   plan {\n        #     id\n        #     name\n        #     price\n        #     remarks\n        #   }\n        # }\n        # zone {\n        #   id\n        #   name\n        # }\n        # collection_day\n        # currentPlan {\n        #   id\n        #   name\n        # }\n      }\n    paginator_info {\n      total\n      current_page\n      last_page\n    }\n  }\n}\n  "];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation GeneratePaymentReceiptLink(\n    $id: ID!\n    $phone_number: String\n    $should_send: Boolean!\n  ) {\n    generatePaymentReceiptLink(\n      id: $id\n      phone_number: $phone_number\n      should_send: $should_send\n    )\n  }\n"): (typeof documents)["\n  mutation GeneratePaymentReceiptLink(\n    $id: ID!\n    $phone_number: String\n    $should_send: Boolean!\n  ) {\n    generatePaymentReceiptLink(\n      id: $id\n      phone_number: $phone_number\n      should_send: $should_send\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation ResendPaymentLink(\n    $customer_id: ID\n    $subscription_id: ID\n    $payable_amount: Float\n    $phone_number: String!\n  ) {\n    resendPaymentLink(\n      subscription_id: $subscription_id\n      phone_number: $phone_number\n      customer_id: $customer_id\n      payable_amount: $payable_amount\n    )\n  }\n"): (typeof documents)["\n  mutation ResendPaymentLink(\n    $customer_id: ID\n    $subscription_id: ID\n    $payable_amount: Float\n    $phone_number: String!\n  ) {\n    resendPaymentLink(\n      subscription_id: $subscription_id\n      phone_number: $phone_number\n      customer_id: $customer_id\n      payable_amount: $payable_amount\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation MarkAsPaid(\n    $subscription_id: ID!\n    $payment_mode: PaymentMode!\n    $remarks: String\n    $payment_date: DateTime\n  ) {\n    markAsPaid(\n      subscription_id: $subscription_id\n      payment_mode: $payment_mode\n      remarks: $remarks\n      payment_date: $payment_date\n    )\n  }\n"): (typeof documents)["\n  mutation MarkAsPaid(\n    $subscription_id: ID!\n    $payment_mode: PaymentMode!\n    $remarks: String\n    $payment_date: DateTime\n  ) {\n    markAsPaid(\n      subscription_id: $subscription_id\n      payment_mode: $payment_mode\n      remarks: $remarks\n      payment_date: $payment_date\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteEstablishment(\n    $id: ID!\n  ) {\n    deleteEstablishment(id: $id)\n  }\n"): (typeof documents)["\n  mutation DeleteEstablishment(\n    $id: ID!\n  ) {\n    deleteEstablishment(id: $id)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddSubscription(\n    $customer_id: ID!\n    $establishment_input: EstablishmentInput!\n    $subscription_input: SubscriptionInput!\n  ) {\n    addSubscription(\n      customer_id: $customer_id\n      establishment_input: $establishment_input\n      subscription_input: $subscription_input\n    )\n  }\n"): (typeof documents)["\n  mutation AddSubscription(\n    $customer_id: ID!\n    $establishment_input: EstablishmentInput!\n    $subscription_input: SubscriptionInput!\n  ) {\n    addSubscription(\n      customer_id: $customer_id\n      establishment_input: $establishment_input\n      subscription_input: $subscription_input\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCustomerSubscriptionHistory($customer_id: ID!, $first: Int!, $page: Int) {\n    getCustomerSubscriptionHistory(customer_id: $customer_id, first: $first, page: $page) {\n      data {\n        id\n        start_date\n        end_date\n        payable_amount\n        customer {\n          id\n          name\n          address\n          phone_number\n        }\n        payment_mode\n        paymentOrder {\n          id\n          paymentable_type\n          paymentable {\n            ... on RzpayPaymentOrder{\n              id\n              payment_status\n              payment_error\n            }\n          }\n        }\n        latestWhatsApp{\n          id\n          message_status\n        }\n        paid_at\n        remarks\n        subscriptionItems {\n          id\n          plan {\n            id\n            name\n            price\n          }\n          establishment {\n            id\n            name\n            address\n            phone_number\n          }\n          period\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCustomerSubscriptionHistory($customer_id: ID!, $first: Int!, $page: Int) {\n    getCustomerSubscriptionHistory(customer_id: $customer_id, first: $first, page: $page) {\n      data {\n        id\n        start_date\n        end_date\n        payable_amount\n        customer {\n          id\n          name\n          address\n          phone_number\n        }\n        payment_mode\n        paymentOrder {\n          id\n          paymentable_type\n          paymentable {\n            ... on RzpayPaymentOrder{\n              id\n              payment_status\n              payment_error\n            }\n          }\n        }\n        latestWhatsApp{\n          id\n          message_status\n        }\n        paid_at\n        remarks\n        subscriptionItems {\n          id\n          plan {\n            id\n            name\n            price\n          }\n          establishment {\n            id\n            name\n            address\n            phone_number\n          }\n          period\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCustomerEstablishments($customer_id: ID!) {\n    getCustomerEstablishment(customer_id: $customer_id) {\n      id\n      name\n      phone_number\n      alt_phone_number\n      address\n      remarks\n      collection_days\n      zone_id\n      current_plan_id\n      current_plan_period\n      subscription_status\n      plan {\n        name\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCustomerEstablishments($customer_id: ID!) {\n    getCustomerEstablishment(customer_id: $customer_id) {\n      id\n      name\n      phone_number\n      alt_phone_number\n      address\n      remarks\n      collection_days\n      zone_id\n      current_plan_id\n      current_plan_period\n      subscription_status\n      plan {\n        name\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateEstablishment(\n    $id: ID!\n    $name: String\n    $phone_number: String\n    $alt_phone_number: String\n    $address: String\n    $collection_days: [CollectionDay!]\n    $current_plan_id: ID\n    $current_plan_period: Int\n    $zone_id: ID\n    $subscription_status: SubscriptionStatus\n  ) {\n    updateEstablishment(\n      id: $id\n      name: $name\n      phone_number: $phone_number\n      alt_phone_number: $alt_phone_number\n      address: $address\n      collection_days: $collection_days\n      current_plan_id: $current_plan_id\n      current_plan_period: $current_plan_period\n      zone_id: $zone_id\n      subscription_status: $subscription_status\n    )\n  }\n"): (typeof documents)["\n  mutation UpdateEstablishment(\n    $id: ID!\n    $name: String\n    $phone_number: String\n    $alt_phone_number: String\n    $address: String\n    $collection_days: [CollectionDay!]\n    $current_plan_id: ID\n    $current_plan_period: Int\n    $zone_id: ID\n    $subscription_status: SubscriptionStatus\n  ) {\n    updateEstablishment(\n      id: $id\n      name: $name\n      phone_number: $phone_number\n      alt_phone_number: $alt_phone_number\n      address: $address\n      collection_days: $collection_days\n      current_plan_id: $current_plan_id\n      current_plan_period: $current_plan_period\n      zone_id: $zone_id\n      subscription_status: $subscription_status\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateProduct(\n    $id: ID!\n    $item_name: String\n    $stock: Int\n    $price: Float\n    $discount: Float\n  ) {\n    updateProduct(id: $id, item_name: $item_name, stock: $stock, price: $price, discount: $discount)\n  }\n"): (typeof documents)["\n  mutation UpdateProduct(\n    $id: ID!\n    $item_name: String\n    $stock: Int\n    $price: Float\n    $discount: Float\n  ) {\n    updateProduct(id: $id, item_name: $item_name, stock: $stock, price: $price, discount: $discount)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetProducts {\n    getProducts {\n      id\n      item_name\n      stock\n      price\n      discount\n    }\n  }\n"): (typeof documents)["\n  query GetProducts {\n    getProducts {\n      id\n      item_name\n      stock\n      price\n      discount\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SearchCustomerByName($name: String!) {\n    getCustomers(first: 20, name: $name) {\n      data {\n        id\n        name\n      }\n    }\n  }\n"): (typeof documents)["\n  query SearchCustomerByName($name: String!) {\n    getCustomers(first: 20, name: $name) {\n      data {\n        id\n        name\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddSale(\n    $customer_id: ID!\n    $payment_mode: PaymentMode!\n    $products: [ProductInput!]!\n  ) {\n    addSale(\n      customer_id: $customer_id\n      payment_mode: $payment_mode\n      products: $products\n    )\n  }\n"): (typeof documents)["\n  mutation AddSale(\n    $customer_id: ID!\n    $payment_mode: PaymentMode!\n    $products: [ProductInput!]!\n  ) {\n    addSale(\n      customer_id: $customer_id\n      payment_mode: $payment_mode\n      products: $products\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteSale(\n    $id: ID!\n  ) {\n    deleteSale(id: $id) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteSale(\n    $id: ID!\n  ) {\n    deleteSale(id: $id) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSales(\n    $first: Int!\n    $page: Int\n    $product_name: String\n    $start_date: DateTime\n    $end_date: DateTime\n  ) {\n    getSales(\n      first: $first\n      page: $page\n      product_name: $product_name\n      start_date: $start_date\n      end_date: $end_date\n    ) {\n      data {\n        id\n        payment_mode\n        customer {\n          id\n          name\n          address\n          phone_number\n        }\n        productSales {\n          id\n          quantity\n          price_per_unit\n          discount\n          product {\n            id\n            item_name\n          }\n        }\n        created_at\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetSales(\n    $first: Int!\n    $page: Int\n    $product_name: String\n    $start_date: DateTime\n    $end_date: DateTime\n  ) {\n    getSales(\n      first: $first\n      page: $page\n      product_name: $product_name\n      start_date: $start_date\n      end_date: $end_date\n    ) {\n      data {\n        id\n        payment_mode\n        customer {\n          id\n          name\n          address\n          phone_number\n        }\n        productSales {\n          id\n          quantity\n          price_per_unit\n          discount\n          product {\n            id\n            item_name\n          }\n        }\n        created_at\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetPaymentRecords(\n    $first: Int!\n    $month: Int!\n    $year: Int!\n    $page: Int\n    $keyword: String\n    $plan_id: ID\n    $is_paid: Boolean\n  ) {\n    getPaymentRecords(\n      first: $first\n      page: $page\n      month: $month\n      year: $year\n      keyword: $keyword\n      plan_id: $plan_id\n      is_paid: $is_paid\n    ) {\n      data {\n        id\n        # overdue\n        start_date\n        end_date\n        payable_amount\n        customer {\n          id\n          name\n          phone_number\n          address\n        }\n        subscriptionItems {\n          id\n          plan {\n            id\n            name\n          }\n          establishment {\n            id\n            name\n            address\n            phone_number\n            zone {\n              id\n              name\n            }\n          }\n        }\n        paid_at\n        payment_mode\n        latestWhatsApp {\n          id\n          message_status\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetPaymentRecords(\n    $first: Int!\n    $month: Int!\n    $year: Int!\n    $page: Int\n    $keyword: String\n    $plan_id: ID\n    $is_paid: Boolean\n  ) {\n    getPaymentRecords(\n      first: $first\n      page: $page\n      month: $month\n      year: $year\n      keyword: $keyword\n      plan_id: $plan_id\n      is_paid: $is_paid\n    ) {\n      data {\n        id\n        # overdue\n        start_date\n        end_date\n        payable_amount\n        customer {\n          id\n          name\n          phone_number\n          address\n        }\n        subscriptionItems {\n          id\n          plan {\n            id\n            name\n          }\n          establishment {\n            id\n            name\n            address\n            phone_number\n            zone {\n              id\n              name\n            }\n          }\n        }\n        paid_at\n        payment_mode\n        latestWhatsApp {\n          id\n          message_status\n        }\n      }\n      paginator_info {\n        total\n        current_page\n        last_page\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddPlan(\n    $name: String!\n    $price: Float!\n    $remarks: String\n    $is_internal: Boolean!\n    $category: PlanCategory!\n  ) {\n    addPlan(name: $name, price: $price, remarks: $remarks, is_internal: $is_internal, category: $category)\n  }\n"): (typeof documents)["\n  mutation AddPlan(\n    $name: String!\n    $price: Float!\n    $remarks: String\n    $is_internal: Boolean!\n    $category: PlanCategory!\n  ) {\n    addPlan(name: $name, price: $price, remarks: $remarks, is_internal: $is_internal, category: $category)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdatePlan(\n    $id: ID!\n    $name: String\n    $price: Float\n    $remarks: String\n    $is_internal: Boolean\n    $category: PlanCategory\n  ) {\n    updatePlan(id: $id, name: $name, price: $price, remarks: $remarks, is_internal: $is_internal, category: $category)\n  }\n"): (typeof documents)["\n  mutation UpdatePlan(\n    $id: ID!\n    $name: String\n    $price: Float\n    $remarks: String\n    $is_internal: Boolean\n    $category: PlanCategory\n  ) {\n    updatePlan(id: $id, name: $name, price: $price, remarks: $remarks, is_internal: $is_internal, category: $category)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeletePlan(\n    $id: ID!\n  ) {\n    deletePlan(id: $id)\n  }\n"): (typeof documents)["\n  mutation DeletePlan(\n    $id: ID!\n  ) {\n    deletePlan(id: $id)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SubscriberPlanDistribution($zone_id: ID) {\n    subscriberPlanDistribution(zone_id: $zone_id) {\n      plan_name\n      total_subscriber\n    }\n  }\n"): (typeof documents)["\n  query SubscriberPlanDistribution($zone_id: ID) {\n    subscriberPlanDistribution(zone_id: $zone_id) {\n      plan_name\n      total_subscriber\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SubscriberGrowthTrend($year: Int!) {\n    subscriberGrowthTrend(year: $year) {\n      month\n      total_subscriber\n    }\n  }\n"): (typeof documents)["\n  query SubscriberGrowthTrend($year: Int!) {\n    subscriberGrowthTrend(year: $year) {\n      month\n      total_subscriber\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SubscriptionPaymentTrend($month: Int!, $year: Int!, $plan_id: ID, $zone_id: ID) {\n    subscriptionPaymentTrend(month: $month, year: $year, plan_id: $plan_id, zone_id: $zone_id) {\n      day\n      total_amount\n    }\n  }\n"): (typeof documents)["\n  query SubscriptionPaymentTrend($month: Int!, $year: Int!, $plan_id: ID, $zone_id: ID) {\n    subscriptionPaymentTrend(month: $month, year: $year, plan_id: $plan_id, zone_id: $zone_id) {\n      day\n      total_amount\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetBasicStats($start_date: DateTime, $end_date: DateTime) {\n    getBasicStats(start_date: $start_date, end_date: $end_date) {\n      total_subscriber\n      total_subscription_fee\n      total_fee_paid\n      total_fee_unpaid\n    }\n  }\n"): (typeof documents)["\n  query GetBasicStats($start_date: DateTime, $end_date: DateTime) {\n    getBasicStats(start_date: $start_date, end_date: $end_date) {\n      total_subscriber\n      total_subscription_fee\n      total_fee_paid\n      total_fee_unpaid\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation ProcessSubscriptionPaymentLink(\n    $payment_link_hash: String!\n  ) {\n    processSubscriptionPaymentLink(\n      payment_link_hash: $payment_link_hash\n    ) {\n      ... on RzpayPaymentOrder {\n        id\n        order_id\n        payment_error\n        payment_id\n        payment_instrument_type\n        payment_status\n        refund_amount\n        refund_error\n        refund_status\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation ProcessSubscriptionPaymentLink(\n    $payment_link_hash: String!\n  ) {\n    processSubscriptionPaymentLink(\n      payment_link_hash: $payment_link_hash\n    ) {\n      ... on RzpayPaymentOrder {\n        id\n        order_id\n        payment_error\n        payment_id\n        payment_instrument_type\n        payment_status\n        refund_amount\n        refund_error\n        refund_status\n      }\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;