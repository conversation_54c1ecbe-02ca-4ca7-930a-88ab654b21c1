# Project Style Guide & Conventions

This document outlines the architecture, coding style, and patterns used in this project. All developers are expected to adhere to these guidelines to maintain consistency and quality in the codebase.

## 1. Core Technologies

- **Framework**: [React](https://react.dev/)
- **Build Tool**: [Vite](https://vitejs.dev/)
- **Language**: [TypeScript](https://www.typescriptlang.org/)
- **Routing**: [React Router v7](https://reactrouter.com/) (with [FS Routes](https://reactrouter.com/en/main/guides/file-based-routing)) for file-based routing
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **UI Components**: [shadcn/ui](https://ui.shadcn.com/)
- **Data Fetching**: [TanStack Query](https://tanstack.com/query/latest) (React Query)
- **URL State Management**: [nuqs](https://nuqs.47ng.com/) for managing state in URL search parameters.
- **API Communication**: [GraphQL](https://graphql.org/) via `graphql-request`
- **Schema Validation**: [Zod](https://zod.dev/)
- **Linting**: [ESLint](https://eslint.org/) with `@antfu/eslint-config`
- **Commit Hooks**: [Husky](https://typicode.github.io/husky/) and [lint-staged](https://github.com/lint-staged/lint-staged)

## 2. Directory Structure

The project's source code is located in the `app/` directory.

```
app/
├── components/         # Reusable React components
│   ├── common/         # Application-specific reusable components
│   ├── icons/          # SVG icon components
│   └── ui/             # Generic UI components from shadcn/ui
├── gql/                # Auto-generated GraphQL types and hooks
├── graphql/            # GraphQL queries, mutations, and fragments
├── hooks/              # Custom React hooks
├── lib/                # Utility functions, constants, and configs
├── routes/             # Application routes (file-based)
├── app.css             # Global styles
├── root.tsx            # Root application component
└── sessions.ts         # Session management logic
```

- **`app/routes/`**: This is where the application's pages and layouts are defined. The structure of this directory maps directly to the URL paths.
- **`app/components/`**: This directory is for all reusable components.
  - `ui/`: Base components provided by `shadcn/ui` (e.g., `button.tsx`, `card.tsx`). Do not modify these directly unless updating the library.
  - `common/`: Components built for this specific application using `ui/` components (e.g., `page-header.tsx`).
  - `icons/`: Custom SVG icons wrapped in React components.
- **`app/hooks/`**: Contains all custom hooks, which are used to encapsulate and reuse stateful logic (e.g., data fetching, form handling).
- **`app/lib/`**: A collection of helper functions, constants, and client initializations (e.g., `graphql-client.ts`, `utils.ts`).
- **`app/graphql/`**: Stores GraphQL operations (queries, mutations). These are often co-located with the features that use them.

## 3. Routing

We use **React Router v7** with its file-system-based routing (`FS Routes`).

- **Creating Routes**: To create a new page, add a `.tsx` file inside `app/routes/`. For example, `app/routes/about.tsx` creates the `/about` page.
- **Nested Routes**: To create a nested route, use a folder structure. For example, `app/routes/dashboard/settings.tsx` creates the `/dashboard/settings` page.
- **Layouts**: Layout routes are created using files prefixed with an underscore (e.g., `_admin/route.tsx`). These layouts wrap nested child routes.
- **Dynamic Segments**: Use `$param` syntax for dynamic routes (e.g., `app/routes/customers_.$id/route.tsx` maps to `/customers/:id`).
- **Data Loading**: Use the `loader` function exported from a route file to fetch data before the component renders.

## 4. Component Architecture

- **Feature-Driven Structure**: Inside `app/routes/`, each feature or page has its own folder containing all related files: the route component (`route.tsx`), data fetching logic (`graphql.ts`), validation schemas (`schema.ts`), and specific components.
- **Composition**: Build complex components by composing smaller, single-purpose components from `app/components/ui` and `app/components/common`.
- **Co-location**: Keep feature-specific logic together. For example, a `customers` feature directory should contain the customer table, dialogs, hooks, and GraphQL queries related to customers.

## 5. State Management & Data Fetching

- **Server State**: **TanStack Query** is the primary tool for managing server state. Use its hooks (`useQuery`, `useMutation`) to fetch and update data.
- **URL State**: **nuqs** is used to manage state that should be persisted in the URL search parameters. This is ideal for filters, search queries, and pagination. Use the `useQueryState` and `useQueryStates` hooks from `nuqs` for this purpose.
- **Custom Hooks**: Encapsulate TanStack Query logic within custom hooks for better reusability and separation of concerns (e.g., `use-get-customers.ts`).
- **GraphQL**: All API communication is done via GraphQL.
  - **Code Generation**: The project uses `graphql-codegen` to generate TypeScript types and typed document nodes from `.ts` files containing GraphQL queries. Run `pnpm codegen` to update these types after changing a query.
  - **Client**: A shared `graphql-request` client is configured in `app/lib/graphql-client.ts`.

## 6. Styling

- **Tailwind CSS**: All styling is done using Tailwind CSS utility classes. Avoid writing custom CSS in `.css` files unless absolutely necessary for global styles or complex animations.
- **`cn` Utility**: Use the `cn` function from `app/lib/utils.ts` to conditionally apply classes. This is the standard from `shadcn/ui`.
- **`cva`**: For components with variants (e.g., buttons with different colors or sizes), use `class-variance-authority`.

## 7. Coding Style & Naming Conventions

- **File Naming**: Use `kebab-case` for all file and directory names (e.g., `customer-table.tsx`, `use-get-customers.ts`).
- **Component Naming**: React components should be named with `PascalCase` (e.g., `CustomerTable`).
- **Hook Naming**: Custom hooks must be prefixed with `use` and named with `PascalCase` (e.g., `useGetCustomers`).
- **TypeScript**: Use TypeScript for all new code. Leverage auto-generated types from GraphQL for type safety.
- **Imports**: Use path aliases defined in `tsconfig.json` for clean imports (e.g., `~/lib/utils` instead of `../../lib/utils`).

## 8. Linting and Formatting

- **ESLint**: The project is configured with a strict ESLint setup. Run `pnpm lint` to check for issues.
- **Pre-commit Hook**: A pre-commit hook is set up with Husky to automatically run `lint-staged`. This ensures that all committed code is properly formatted and free of linting errors.
- **VS Code**: If you use VS Code, it's recommended to install the ESLint extension to get real-time feedback.