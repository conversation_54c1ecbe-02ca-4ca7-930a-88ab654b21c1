name: Deploy build to production Server

on:
  push:
    branches:
      - production

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 22.11.0

      - name: Install dependencies
        run: npm i

      - name: Build project
        run: |
          touch .env
          echo VITE_BASE_URL=https://api.aizawlgarbo.com >> .env
          echo VITE_RZPAY_KEY=rzp_test_vYL90KSln23VS3 >> .env
          npm run build:production

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p 2223 ${{secrets.SSH_HOST}} >> ~/.ssh/known_hosts

      - name: Copy files to VPS and restart PM2
        run: |
          rsync -avz -e "ssh -p 2223" ./* ${{secrets.SSH_USER}}@${{secrets.SSH_HOST}}:/home/<USER>/web/
          ssh ${{secrets.SSH_USER}}@${{secrets.SSH_HOST}} -p 2223 '/home/<USER>/.volta/bin/pm2 restart web'
